#!/usr/bin/env python3
"""
Unified Superlet Demo – Full TFR Viewer
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

try:
    # Try direct imports first
    from superlet import superlets as superlet_original
    from superletcx import superlets as superlet_cx
except ImportError:
    try:
        # Try package-style imports
        from code.superlet import superlets as superlet_original
        from code.superletcx import superlets as superlet_cx
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Make sure you're running this script from the demo_script directory")
        print("and that the code directory contains superlet.py and superletcx.py")
        print(f"Code directory: {code_dir}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

# Use superletcx as the default superlets function for this demo
superlets = superlet_cx

def generate_test_signal(fs=1024, duration=2.0):
    """Generate a simple test signal with known frequency components."""
    t = np.linspace(0, duration, int(fs * duration))
    
    # Create signal with multiple frequency components
    signal = (np.sin(2 * np.pi * 20 * t) +  # 20 Hz component
              np.sin(2 * np.pi * 40 * t) +  # 40 Hz component  
              np.sin(2 * np.pi * 60 * t))   # 60 Hz component
    
    # Add some noise
    np.random.seed(42)
    signal += 0.1 * np.random.randn(len(t))
    
    return t, signal

if __name__ == "__main__":
    fs = 1024
    t, signal = generate_test_signal(fs)
    
    freqs = np.linspace(10, 80, 71)
    c1 = 3
    orders = (1, 10)
    
    spectrum = superlets(signal, fs, freqs, c1, orders)
    
    magnitude = np.abs(spectrum)
    phase = np.angle(spectrum)
    real_part = np.real(spectrum)  # real component for voice with oscillation
    
    fig, axes = plt.subplots(1, 4, figsize=(20, 5), gridspec_kw={'width_ratios': [1.5, 2, 2, 2]})
    
    # Original Signal with time on y-axis
    axes[0].plot(signal, t, color='black', linewidth=0.8)
    axes[0].set_title('Original Signal')
    axes[0].set_xlabel('Amplitude')
    axes[0].set_ylabel('Time (s)')
    axes[0].grid(True, alpha=0.3)
    axes[0].set_ylim(t[0], t[-1])
    axes[0].invert_yaxis()  # Makes time increase downward to match TFR plots

    # Magnitude TFR
    im0 = axes[1].imshow(magnitude.T, aspect='auto', origin='lower',
                         extent=[freqs[0], freqs[-1], t[0], t[-1]],
                         cmap='viridis')
    axes[1].set_title('Magnitude TFR')
    axes[1].set_xlabel('Frequency (Hz)')
    axes[1].set_ylabel('Time (s)')
    plt.colorbar(im0, ax=axes[1])
    
    # Phase TFR
    im1 = axes[2].imshow(phase.T, aspect='auto', origin='lower',
                         extent=[freqs[0], freqs[-1], t[0], t[-1]],
                         cmap='hsv')
    axes[2].set_title('Phase TFR')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_ylabel('Time (s)')
    plt.colorbar(im1, ax=axes[2])
    
    # Voice (real part) TFR with oscillation
    vmax = np.max(np.abs(real_part))
    im2 = axes[3].imshow(real_part.T, aspect='auto', origin='lower',
                         extent=[freqs[0], freqs[-1], t[0], t[-1]],
                         cmap='RdBu', vmin=-vmax, vmax=vmax)
    axes[3].set_title('Voice (Real Part) TFR')
    axes[3].set_xlabel('Frequency (Hz)')
    axes[3].set_ylabel('Time (s)')
    plt.colorbar(im2, ax=axes[3])
    
    plt.tight_layout()
    plt.savefig('superlet_full_demo.png')
    plt.show()