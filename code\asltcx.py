import numpy as np

def asltcx(input_data, Fs, F, Ncyc, ord=None, mult=False):
    """
    Modified aslt to output complex spectrum.
    """
    # --- Input Validation ---
    if F is None or len(F) == 0:
        raise ValueError("Frequencies not defined.")

    if input_data is None or input_data.size == 0:
        raise ValueError("Input data is empty.")

    # Ensure input_data is a 2D array
    if input_data.ndim == 1:
        input_data = input_data[np.newaxis, :]

    # --- Initialization ---
    if ord is not None and len(ord) == 2:
        order_ls = np.round(np.linspace(ord[0], ord[1], len(F))).astype(int)
    else:
        # Default to order 1 for all frequencies (standard CWT)
        order_ls = np.ones(len(F), dtype=int)

    Nbuffers, Npoints = input_data.shape
    padding = 0
    
    # Create a list of lists to store the wavelets
    wavelets = [[] for _ in range(len(F))]

    # --- Generate Wavelet Sets ---
    if mult:
        # Multiplicative superresolution
        for i_freq in range(len(F)):
            for i_ord in range(1, order_ls[i_freq] + 1):
                # Each new wavelet has Ncyc extra cycles
                w = cxmorlet(F[i_freq], Ncyc * i_ord, Fs)
                wavelets[i_freq].append(w)
                padding = max(padding, int(len(w) / 2))
    else:
        # Additive superresolution
        for i_freq in range(len(F)):
            for i_ord in range(order_ls[i_freq]):
                # Each new wavelet has an extra cycle
                w = cxmorlet(F[i_freq], Ncyc + i_ord, Fs)
                wavelets[i_freq].append(w)
                padding = max(padding, int(len(w) / 2))

    # --- Main Computation ---
    buffer = np.zeros(Npoints + 2 * padding, dtype=np.complex128)
    wtresult = np.zeros((len(F), Npoints), dtype=np.complex128)
    
    # Convenience indices for the zero-padded buffer
    bufbegin = padding
    bufend = padding + Npoints

    for i_buf in range(Nbuffers):
        for i_freq in range(len(F)):
            # Init magnitude and phase buffers
            mag_buffer = np.ones(Npoints, dtype=np.float64)
            phase_buffer = np.zeros(Npoints, dtype=np.complex128)
            
            # Fill the central part of the buffer with input data
            buffer[bufbegin:bufend] = input_data[i_buf, :]
            
            # Convolve with each wavelet in the current set
            for i_ord in range(order_ls[i_freq]):
                # Restricted convolution (input size == output size)
                tempcx = np.convolve(buffer, wavelets[i_freq][i_ord], mode='same')[bufbegin:bufend]
                
                abs_conv = np.abs(tempcx)
                mag_buffer *= abs_conv
                phase_buffer += tempcx / (abs_conv + 1e-10)  # Avoid division by zero

            # Compute the geometric mean for magnitude, arithmetic for phase
            root = 1 / order_ls[i_freq]
            magnitude = mag_buffer ** root
            phase = phase_buffer / order_ls[i_freq]
            
            # Combine to complex and accumulate
            wtresult[i_freq, :] += magnitude * phase

    # Scale the output by the number of input buffers
    wtresult /= Nbuffers
    
    return wtresult


def cxmorlet(Fc, Nc, Fs):
    """
    Computes the complex Morlet wavelet.
    """
    # The last peak should be at 2.5 standard deviations
    sd = (Nc / 2) * (1 / Fc) / 2.5
    wl = 2 * np.floor(int(6 * sd * Fs) / 2) + 1
    w = np.zeros(int(wl), dtype=np.complex128)
    gi = 0
    off = int(wl / 2)

    for i in range(int(wl)):
        t = (i - off) / Fs
        w[i] = bw_cf(t, sd, Fc)
        gi += gauss(t, sd)

    return w / gi

def bw_cf(t, bw, cf):
    """
    Computes complex wavelet coefficients.
    """
    cnorm = 1 / (bw * np.sqrt(2 * np.pi))
    exp1 = cnorm * np.exp(-(t**2) / (2 * bw**2))
    return np.exp(2j * np.pi * cf * t) * exp1

def gauss(t, sd):
    """
    Computes the Gaussian coefficient.
    """
    cnorm = 1 / (sd * np.sqrt(2 * np.pi))
    return cnorm * np.exp(-(t**2) / (2 * sd**2))