#!/usr/bin/env python3
"""
Superlet vs DLOGST Spectral Descriptor Comparison with Synthetic Data
Creates a 7-column comparison plot showing:
1. Signal
2-4. Superlet: Magnitude, Voice, Magnitude*Voice
5-7. DLOGST: Magnitude, Voice, Magnitude*Voice
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

# Add the reference directory to the Python path
reference_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'reference')
if reference_dir not in sys.path:
    sys.path.insert(0, reference_dir)

# Add the root directory to the Python path for package imports
root_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..')
if root_dir not in sys.path:
    sys.path.insert(0, root_dir)

try:
    # Try direct imports first
    from superletcx import superlets
    from dlogst_spec_descriptor import dlogst_spec_descriptor
except ImportError:
    try:
        # Try package-style imports
        from code.superletcx import superlets
        from reference.dlogst_spec_descriptor import dlogst_spec_descriptor
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Make sure you're running this script from the demo_script directory")
        print("and that the code and reference directories contain the required modules")
        print(f"Code directory: {code_dir}")
        print(f"Reference directory: {reference_dir}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

def generate_synthetic_signal(fs=1000, duration=2.0):
    """Generate a synthetic signal with time-varying frequency content."""
    t = np.linspace(0, duration, int(fs * duration))
    
    # Create a signal with multiple frequency components that vary over time
    signal = np.zeros_like(t)
    
    # Low frequency component (10-20 Hz) - present throughout
    signal += 0.8 * np.sin(2 * np.pi * (10 + 5 * t / duration) * t)
    
    # Medium frequency component (30-50 Hz) - stronger in middle
    envelope1 = np.exp(-((t - duration/2) / (duration/4))**2)
    signal += 0.6 * envelope1 * np.sin(2 * np.pi * (30 + 10 * t / duration) * t)
    
    # High frequency component (60-80 Hz) - present in first half
    envelope2 = np.exp(-((t - duration/4) / (duration/6))**2)
    signal += 0.4 * envelope2 * np.sin(2 * np.pi * (60 + 10 * t / duration) * t)
    
    # Add some noise
    np.random.seed(42)
    signal += 0.1 * np.random.randn(len(t))
    
    return t, signal

def create_comparison_plot(signal, time, fs, 
                          freq_range=(1, 100), 
                          c1=3, orders=(1, 10),
                          shape=0.35, kmax=120, int_val=35,
                          colormap='viridis'):
    """Create the 7-column comparison plot."""
    
    dt = 1.0 / fs
    fmax_hz = freq_range[1]
    fmax_samples = int(fmax_hz * len(signal) / fs)
    
    # Frequency array for superlet
    freqs = np.linspace(freq_range[0], fmax_hz, 80)
    
    print("Computing Superlet Transform...")
    # Compute Superlet Transform
    superlet_spectrum = superlets(signal, fs, freqs, c1, orders)
    superlet_magnitude = np.abs(superlet_spectrum)
    superlet_voice = np.real(superlet_spectrum)  # Real part as voice
    superlet_mag_voice = superlet_magnitude * superlet_voice
    
    print("Computing DLOGST Spectral Descriptor...")
    # Compute DLOGST - only unpack the variables we need
    dlogst_results = dlogst_spec_descriptor(signal, dt, fmax_samples, shape, kmax, int_val)
    # Extract only the needed variables: MST, dlogst_mag, dlogst_phase, dlogst_voice, peak_freq, freq_loc, spec_centroid,
    # spec_slope, mag_voice_slope, voice_slope, spec_decrease, time_dlogst, freqst
    _, dlogst_mag, _, dlogst_voice, _, _, _, _, _, _, _, _, freqst = dlogst_results
    
    dlogst_mag_voice = dlogst_mag * dlogst_voice
    
    # Limit frequency range for plotting
    freq_limit_dlogst = np.argmin(np.abs(freqst - fmax_hz))
    
    print("Creating comparison plot...")
    # Create the 7-column comparison plot
    fig, axes = plt.subplots(1, 7, figsize=(28, 10), sharey=True)
    fig.suptitle('Superlet vs DLOGST Comparison - Synthetic Signal', fontsize=16)
    
    # Column 1: Signal
    axes[0].plot(signal, time)
    axes[0].set_title('Signal')
    axes[0].set_xlabel('Amplitude')
    axes[0].set_ylabel('Time (s)')
    axes[0].grid(True, alpha=0.3)
    
    # Columns 2-4: Superlet results
    # Magnitude
    im1 = axes[1].pcolormesh(freqs, time, superlet_magnitude.T, 
                            shading='auto', cmap=colormap)
    axes[1].set_title('Superlet Magnitude')
    axes[1].set_xlabel('Frequency (Hz)')
    axes[1].set_xlim(freq_range)
    plt.colorbar(im1, ax=axes[1], shrink=0.8)
    
    # Voice
    vmax_superlet = np.max(np.abs(superlet_voice))
    im2 = axes[2].pcolormesh(freqs, time, superlet_voice.T, 
                            shading='auto', cmap='RdBu', 
                            vmin=-vmax_superlet, vmax=vmax_superlet)
    axes[2].set_title('Superlet Voice')
    axes[2].set_xlabel('Frequency (Hz)')
    axes[2].set_xlim(freq_range)
    plt.colorbar(im2, ax=axes[2], shrink=0.8)
    
    # Magnitude * Voice
    im3 = axes[3].pcolormesh(freqs, time, superlet_mag_voice.T, 
                            shading='auto', cmap=colormap)
    axes[3].set_title('Superlet Mag×Voice')
    axes[3].set_xlabel('Frequency (Hz)')
    axes[3].set_xlim(freq_range)
    plt.colorbar(im3, ax=axes[3], shrink=0.8)
    
    # Columns 5-7: DLOGST results
    # Magnitude
    im4 = axes[4].pcolormesh(freqst[:freq_limit_dlogst], time, 
                            dlogst_mag[:freq_limit_dlogst, :].T, 
                            shading='auto', cmap=colormap)
    axes[4].set_title('DLOGST Magnitude')
    axes[4].set_xlabel('Frequency (Hz)')
    axes[4].set_xlim(freq_range)
    plt.colorbar(im4, ax=axes[4], shrink=0.8)
    
    # Voice
    vmax_dlogst = np.max(np.abs(dlogst_voice[:freq_limit_dlogst, :]))
    im5 = axes[5].pcolormesh(freqst[:freq_limit_dlogst], time, 
                            dlogst_voice[:freq_limit_dlogst, :].T, 
                            shading='auto', cmap='RdBu',
                            vmin=-vmax_dlogst, vmax=vmax_dlogst)
    axes[5].set_title('DLOGST Voice')
    axes[5].set_xlabel('Frequency (Hz)')
    axes[5].set_xlim(freq_range)
    plt.colorbar(im5, ax=axes[5], shrink=0.8)
    
    # Magnitude * Voice
    im6 = axes[6].pcolormesh(freqst[:freq_limit_dlogst], time, 
                            dlogst_mag_voice[:freq_limit_dlogst, :].T, 
                            shading='auto', cmap=colormap)
    axes[6].set_title('DLOGST Mag×Voice')
    axes[6].set_xlabel('Frequency (Hz)')
    axes[6].set_xlim(freq_range)
    plt.colorbar(im6, ax=axes[6], shrink=0.8)
    
    # Set time limits for all axes (invert for seismic convention)
    for ax in axes:
        ax.set_ylim(time[-1], time[0])  # Reverse for seismic convention
        if ax != axes[0]:  # Don't set ylabel for signal plot
            ax.set_ylabel('')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    return fig, axes

def main():
    """Main function to create the comparison plot."""
    # Generate synthetic signal
    fs = 1000  # Sampling frequency
    duration = 2.0  # Duration in seconds
    time, signal = generate_synthetic_signal(fs, duration)
    
    print(f"Generated signal: {len(signal)} samples, {duration}s duration, {fs}Hz sampling rate")
    
    # Parameters
    freq_range = (5, 100)  # Frequency range for analysis
    c1 = 3  # Superlet base cycles
    orders = (1, 10)  # Superlet orders
    shape = 0.35  # DLOGST shape parameter
    kmax = 120  # DLOGST kmax parameter
    int_val = 35  # DLOGST intercept parameter
    colormap = 'viridis'  # Colormap for plots
    
    # Create comparison plot
    fig, _ = create_comparison_plot(
        signal, time, fs,
        freq_range=freq_range,
        c1=c1, orders=orders,
        shape=shape, kmax=kmax, int_val=int_val,
        colormap=colormap
    )
    
    # Save the figure
    plt.savefig('superlet_vs_dlogst_synthetic_comparison.png', dpi=300, bbox_inches='tight')
    print("Plot saved as 'superlet_vs_dlogst_synthetic_comparison.png'")
    
    # Print some statistics
    print(f"\nSignal statistics:")
    print(f"  Duration: {duration}s")
    print(f"  Sampling rate: {fs}Hz")
    print(f"  Signal range: {np.min(signal):.3f} to {np.max(signal):.3f}")
    print(f"  Frequency analysis range: {freq_range[0]}-{freq_range[1]}Hz")
    
    plt.show()

if __name__ == "__main__":
    main()
