# Extending the Superlet Transform to Complex Spectrum Output: Mathematical Reasoning and Code Modifications

## Introduction

The Superlet Transform (SLT), as introduced in the paper *"Time-frequency super-resolution with superlets"* by <PERSON><PERSON> et al. (2021, Nature Communications), is an extension of the Continuous Wavelet Transform (CWT) designed to achieve super-resolution in time-frequency (TF) analysis. It addresses the Heisenberg-Gabor uncertainty principle by combining sets of Morlet wavelets (superlets) with the same center frequency but increasing bandwidth constraints, yielding improved resolution for signals with finite oscillation bursts, such as seismic or neural data.

The original implementations in `faslt.py` (Fractional Adaptive Superlet Transform) and `aslt.py` (Adaptive Superlet Transform) produce real-valued magnitude or power spectra, discarding phase information. Extending these to complex outputs (similar to `superletcx.py`) preserves both magnitude and phase, enabling advanced analyses like instantaneous frequency estimation, phase-amplitude coupling, or signal reconstruction.

This document explains:
- The mathematical fundamentals of the SLT.
- Step-by-step derivation for complex-valued output.
- Specific modifications to `faslt.py` and `aslt.py`.
- How these changes are inspired by `superletcx.py`.

Equations are derived from the original SLT principles, with extensions for complex representation.

## Mathematical Fundamentals of the Superlet Transform

### Morlet Wavelet Basis
The SLT is built on the complex Morlet wavelet, a Gaussian-windowed sinusoid:

\[
\psi(t) = A \exp\left(-\frac{t^2}{2\sigma^2}\right) \exp(i 2\pi f_c t)
\]

- \(A\): Normalization constant (ensures unit energy).
- \(\sigma\): Time spread (standard deviation of Gaussian envelope), related to bandwidth.
- \(f_c\): Center frequency.
- Number of cycles \(c\): Controls trade-off; \(c = \frac{f_c}{\text{bandwidth}}\). More cycles → narrower frequency bandwidth (better frequency resolution), but wider temporal support (poorer time resolution).

The wavelet transform of signal \(s(t)\) at scale corresponding to \(f_c\) and cycles \(c_k\):

\[
W_k(t) = s(t) * \psi_k(t)
\]

Where \(\psi_k\) has cycles \(c_k\), and \(*\) is convolution. \(W_k(t)\) is complex: \(W_k(t) = |W_k(t)| e^{i \phi_k(t)}\).

### Superlet Combination (Original Real-Valued)
A superlet at frequency \(f\) and order \(o\) combines \(o\) wavelets with increasing cycles:
- **Additive**: \(c_k = c_1 + (k-1)\).
- **Multiplicative**: \(c_k = k \cdot c_1\).

The original SLT response is the geometric mean of magnitudes (real-valued):

\[
\text{SL}(t) = \left( \prod_{k=1}^{o} |W_k(t)| \right)^{1/o}
\]

Power: \(|\text{SL}(t)|^2\) (often scaled by 2 for energy conservation).

For adaptive variants (ASLT/FASLT), order \(o(f)\) increases with frequency:

\[
o(f) = o_{\min} + (o_{\max} - o_{\min}) \cdot \frac{f - f_{\min}}{f_{\max} - f_{\min}}
\]

This is integer in ASLT (rounded), fractional in FASLT (\(o = n + \delta\)):

\[
\text{SL}(t) = \left( \prod_{k=1}^{n} |W_k(t)| \cdot |W_{n+1}(t)|^\delta \right)^{1/o}
\]

Phase is discarded, focusing on energy/amplitude.

## Derivation for Complex-Valued Output

To extend to complex output, decompose into magnitude (geometric mean) and phase (arithmetic mean of unit phasors). This preserves the robustness of geometric mean for amplitudes while averaging phases coherently.

### Step 1: Decomposition
Each \(W_k(t) = |W_k(t)| \cdot e^{i \phi_k(t)}\).

Unit phasor: \(u_k(t) = e^{i \phi_k(t)} = \frac{W_k(t)}{|W_k(t)| + \epsilon}\) (\(\epsilon = 10^{-10}\) for stability, avoids division by zero).

### Step 2: Geometric Mean for Magnitude
Retain original:

\[
M(t) = \left( \prod_{k=1}^{o} |W_k(t)| \right)^{1/o}
\]

Fractional:

\[
M(t) = \left( \prod_{k=1}^{n} |W_k(t)| \cdot |W_{n+1}(t)|^\delta \right)^{1/o}
\]

\(M(t)\) is real and positive, robust to outliers (geometric mean emphasizes consistency across wavelets).

### Step 3: Arithmetic Mean for Phase
Average unit phasors (vector sum in complex plane):

\[
U(t) = \frac{1}{o} \sum_{k=1}^{o} u_k(t)
\]

Fractional:

\[
U(t) = \frac{1}{o} \left( \sum_{k=1}^{n} u_k(t) + \delta \cdot u_{n+1}(t) \right)
\]

- \(U(t)\) is complex, \(|U(t)| \leq 1\) (measures phase coherence across the superlet set; \(|U|=1\) if all phases align).
- Arithmetic mean on phasors avoids phase wrapping (better than averaging angles directly).
- Derivation rationale: Phases from similar-bandwidth wavelets should align for true signals; incoherent phases (noise) reduce \(|U|\), suppressing artifacts.

### Step 4: Recombination to Complex SLT
\[
\text{SL}_{\text{complex}}(t) = M(t) \cdot U(t)
\]

- Magnitude: \(|\text{SL}_{\text{complex}}(t)| = M(t) \cdot |U(t)|\) (geometric mean scaled by coherence).
- Phase: \(\arg(\text{SL}_{\text{complex}}(t)) = \arg(U(t))\) (coherent average phase).

For power (if needed): \(|\text{SL}_{\text{complex}}(t)|^2\).

**Why This Form?**
- Geometric mean on magnitude inherits SLT's super-resolution (combines multi-bandwidth views robustly).
- Phasor averaging is standard in signal processing (e.g., coherent averaging in beamforming) and matches `superletcx.py`'s approach (sum of conv/abs for phase).
- Fractional weighting ensures smooth order transitions, consistent with original FASLT.

## Modifications to Original Scripts

### What Needs to Be Modified
In both `faslt.py` and `aslt.py`:
- **Temp Buffer**: Originally multiplies \( (2 \times |conv|^2) \) for power. Change to separate `mag_buffer` (multiply |conv|) and `phase_buffer` (add conv / |conv|).
- **Normalization**: Remove factor 2 (complex envelope, not power). For fractional: mag *= |conv|**exponent, phase += (conv/|conv|)*exponent.
- **Output Combination**: mag**(1/order) * (phase / order) → complex.
- **Dtypes**: Buffers/results to complex128.
- **Stability**: Add epsilon to |conv| in phase.
- **Power**: If needed, compute post-hoc.

These preserve original math but extend to complex.

### How Changes Are Inspired by `superletcx.py`
`superletcx.py` computes complex SLT explicitly:
- Magnitude: Geometric mean of absolutes (pool via *= abs).
- Phase: Sum of unit phasors (conv / abs).
- Fractional: Exponent on extra abs, weight on extra phasor.
- Combine: mag**rfactor * (phase / order).

Modifications mirror this: Separate mag/phase paths, arithmetic phasor mean, geometric mag mean. This ensures consistency with the paper's principles while enabling phase-preserving analysis.

## Modified Code Examples

### Modified `faslt.py`
```python
# [Insert the modified faslt code from previous response here]
```

### Modified `aslt.py`
```python
# [Insert the modified aslt code from previous response here]
```

Test with synthetic signals (e.g., chirps) to validate: Compare |result| to original magnitude, arg(result) for phase tracking.