#!/usr/bin/env python3
"""
Fractional Adaptive Superlet Transform (FASLT) Testing Script

This script demonstrates the application of the Fractional Adaptive Superlet Transform 
for time-frequency analysis using the implementation in faslt.py.
Based on demo_superlet.py but adapted for the FASLT implementation.

The FASLT supports fractional superresolution orders, providing enhanced flexibility
in time-frequency analysis compared to integer-only approaches.
"""

import numpy as np
import matplotlib.pyplot as plt
# Configure smaller figure and font sizes for better on-screen display
plt.rcParams.update({
    'font.size': 8,      # Reduce default text size
    'figure.dpi': 120    # Lower DPI so pixel dimensions are smaller
})
import time
import sys
import os

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

try:
    # Try direct imports first
    from faslt import faslt
except ImportError:
    try:
        # Try package-style imports
        from code.faslt import faslt
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Make sure you're running this script from the demo_script directory")
        print("and that the code directory contains faslt.py")
        print(f"Code directory: {code_dir}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

# Try to import other modules but don't fail if they don't work
try:
    from aslt import aslt
    aslt_available = True
except ImportError:
    try:
        from code.aslt import aslt
        aslt_available = True
    except ImportError:
        aslt_available = False
        print("Warning: ASLT module not available")

try:
    from nfaslt import nfaslt
    nfaslt_available = True
except ImportError:
    try:
        from code.nfaslt import nfaslt
        nfaslt_available = True
    except ImportError:
        nfaslt_available = False
        print("Warning: NFASLT module not available")

# Signal generation parameters
fs = 1024  # Sampling rate
burst_freqs = [20, 40, 60]  # Frequencies of signal bursts
f_shift = 10  # Frequency shift for contamination
n_cycles = 11  # Number of cycles for main bursts
n_neighb_cycles = 12  # Number of cycles for neighboring bursts

print("Generating synthetic signal...")

# Generate synthetic signal similar to demo_superlet.py
ys = []
for f in burst_freqs:
    # Main burst
    t = 1/f * n_cycles
    x = np.linspace(0, t, int(t * fs))
    y = np.sin(2*np.pi*f*x) + np.sin(2*np.pi*(f+f_shift)*x - np.pi/1.5)
    ys.append(y)
    
    # Neighboring burst
    t2 = 1/f * n_neighb_cycles
    x2 = np.linspace(0, t2, int(t2 * fs))
    y2 = np.sin(2*np.pi*(f+15)*x2) + np.sin(2*np.pi*(f+25)*x2 - np.pi/3)
    ys.append(y2)

# Concatenate all signal components
signal = np.concatenate(ys)

# Add some noise for realism
np.random.seed(42)
noise_level = 0.1
signal += noise_level * np.random.randn(len(signal))

print(f"Generated signal with {len(signal)} samples at {fs} Hz")

# Time vector for plotting
time_vector = np.arange(len(signal)) / fs

# Frequencies for analysis
freqs = np.linspace(10, 80, 141)

# Create figure directory
import os
os.makedirs('figures', exist_ok=True)

# Plot the generated signal
fig, ax = plt.subplots(figsize=(8, 2), dpi=120)
ax.plot(time_vector, signal, linewidth=0.8, color='black')
ax.set_xlabel('Time (s)')
ax.set_ylabel('Amplitude')
ax.set_title('Synthetic Test Signal for FASLT Analysis')
ax.grid(True, alpha=0.3)
ax.set_xlim(0, time_vector[-1])
plt.tight_layout()
plt.savefig('figures/faslt_signal.png', dpi=300, bbox_inches='tight')
plt.show()

print("\n" + "="*60)
print("TESTING FRACTIONAL ADAPTIVE SUPERLET TRANSFORMS")
print("="*60)

# Test 1: Basic wavelet transform (order=None gives CWT)
print("\n1. Testing basic wavelet transform (order=None)...")

# Parameters for basic wavelet
c1_basic = 10

# Using faslt.py (order=None gives standard CWT)
spectrum_basic = faslt(signal, fs, freqs, c1_basic, None, 0)

# Plot basic wavelet transform
fig, ax = plt.subplots(figsize=(7, 3), dpi=120)
im = ax.imshow(np.abs(spectrum_basic), aspect='auto', origin='lower',
               extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
               cmap='viridis')
ax.set_xlabel('Time (s)')
ax.set_ylabel('Frequency (Hz)')
ax.set_title(f'Basic Wavelet Transform via FASLT (c1={c1_basic})')
plt.colorbar(im, ax=ax, label='Magnitude')
plt.tight_layout()
plt.savefig('figures/faslt_wavelet_basic.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 2: FASLT with different fractional orders
print("\n2. Testing FASLT with different fractional orders...")

# Parameters for FASLT
c1 = 3  # Base cycles
order_ranges = [[1, 5.5], [1, 10.2], [1, 15.7], [1, 30.3]]  # Different fractional order ranges

fig, axes = plt.subplots(2, 2, figsize=(9, 6), dpi=120)
axes = axes.flatten()

for i, order_range in enumerate(order_ranges):
    print(f"   Processing fractional order range {order_range}...")
    
    # Using faslt.py with fractional orders
    start_time = time.time()
    spectrum = faslt(signal, fs, freqs, c1, order_range, 0)  # 0 = additive
    time_faslt = time.time() - start_time
    
    print(f"      FASLT: {time_faslt:.3f}s")
    
    # Plot results
    im = axes[i].imshow(np.abs(spectrum), aspect='auto', origin='lower',
                        extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                        cmap='viridis')
    axes[i].set_xlabel('Time (s)')
    axes[i].set_ylabel('Frequency (Hz)')
    axes[i].set_title(f'FASLT (c1={c1}, orders={order_range[0]}-{order_range[1]})')
    plt.colorbar(im, ax=axes[i], label='Magnitude')

plt.suptitle('Fractional Adaptive Superlet Transform Results', fontsize=14)
plt.tight_layout()
plt.savefig('figures/faslt_fractional_orders.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 3: Comparison between additive and multiplicative superresolution
print("\n3. Comparing additive vs multiplicative superresolution...")

# Parameters for comparison
c1_comp = 5
order_comp = [1, 15.5]  # Fractional order range

fig, axes = plt.subplots(2, 2, figsize=(10, 6), dpi=120)

# Additive superresolution
print("   Additive superresolution...")
spectrum_add = faslt(signal, fs, freqs, c1_comp, order_comp, 0)

# Multiplicative superresolution  
print("   Multiplicative superresolution...")
spectrum_mult = faslt(signal, fs, freqs, c1_comp, order_comp, 1)

# Plot comparisons
ax1 = axes[0, 0]
im1 = ax1.imshow(np.abs(spectrum_add), aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='viridis')
ax1.set_title('Additive Superresolution')
ax1.set_xlabel('Time (s)')
ax1.set_ylabel('Frequency (Hz)')
plt.colorbar(im1, ax=ax1, label='Magnitude')

ax2 = axes[0, 1]
im2 = ax2.imshow(np.abs(spectrum_mult), aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='viridis')
ax2.set_title('Multiplicative Superresolution')
ax2.set_xlabel('Time (s)')
ax2.set_ylabel('Frequency (Hz)')
plt.colorbar(im2, ax=ax2, label='Magnitude')

# Difference plot
ax3 = axes[1, 0]
diff = np.abs(spectrum_add) - np.abs(spectrum_mult)
im3 = ax3.imshow(diff, aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='RdBu_r', vmin=-np.max(np.abs(diff)), vmax=np.max(np.abs(diff)))
ax3.set_title('Difference (Additive - Multiplicative)')
ax3.set_xlabel('Time (s)')
ax3.set_ylabel('Frequency (Hz)')
plt.colorbar(im3, ax=ax3, label='Difference')

# Relative difference
ax4 = axes[1, 1]
rel_diff = 2 * np.abs(spectrum_add - spectrum_mult) / (np.abs(spectrum_add) + np.abs(spectrum_mult) + 1e-10)
im4 = ax4.imshow(rel_diff, aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='hot', vmin=0, vmax=0.1)
ax4.set_title('Relative Difference')
ax4.set_xlabel('Time (s)')
ax4.set_ylabel('Frequency (Hz)')
plt.colorbar(im4, ax=ax4, label='Relative Difference')

plt.suptitle(f'Superresolution Mode Comparison (c1={c1_comp}, orders={order_comp})', fontsize=14)
plt.tight_layout()
plt.savefig('figures/faslt_superresolution_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 4: Comparison between FASLT, ASLT, and NFASLT
print("\n4. Comparing FASLT vs ASLT vs NFASLT...")

# Parameters for comparison
c1_comp2 = 3
order_comp2 = [1, 10]  # Integer order range for fair comparison

fig, axes = plt.subplots(2, 2, figsize=(10, 6), dpi=120)

# FASLT results
print("   FASLT...")
spectrum_faslt = faslt(signal, fs, freqs, c1_comp2, order_comp2, 0)

# ASLT results (integer orders only)
if aslt_available:
    print("   ASLT...")
    try:
        spectrum_aslt = aslt(signal, fs, freqs, c1_comp2, order_comp2, False)
        aslt_success = True
    except Exception as e:
        print(f"   ASLT failed: {e}")
        spectrum_aslt = np.zeros_like(spectrum_faslt)
        aslt_success = False
else:
    print("   ASLT not available...")
    spectrum_aslt = np.zeros_like(spectrum_faslt)
    aslt_success = False

# NFASLT results (using frequency interval)
if nfaslt_available:
    print("   NFASLT...")
    try:
        freq_interval = [freqs[0], freqs[-1]]
        spectrum_nfaslt = nfaslt(signal, fs, freq_interval, len(freqs), c1_comp2, order_comp2, False)
        nfaslt_success = True
    except Exception as e:
        print(f"   NFASLT failed: {e}")
        spectrum_nfaslt = np.zeros_like(spectrum_faslt)
        nfaslt_success = False
else:
    print("   NFASLT not available...")
    spectrum_nfaslt = np.zeros_like(spectrum_faslt)
    nfaslt_success = False

# Plot comparisons
ax1 = axes[0, 0]
im1 = ax1.imshow(np.abs(spectrum_faslt), aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='viridis')
ax1.set_title('FASLT Implementation')
ax1.set_xlabel('Time (s)')
ax1.set_ylabel('Frequency (Hz)')
plt.colorbar(im1, ax=ax1, label='Magnitude')

ax2 = axes[0, 1]
im2 = ax2.imshow(np.abs(spectrum_aslt), aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='viridis')
aslt_title = 'ASLT Implementation' if aslt_success else 'ASLT Implementation (Failed)'
ax2.set_title(aslt_title)
ax2.set_xlabel('Time (s)')
ax2.set_ylabel('Frequency (Hz)')
plt.colorbar(im2, ax=ax2, label='Magnitude')

ax3 = axes[1, 0]
im3 = ax3.imshow(np.abs(spectrum_nfaslt), aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='viridis')
nfaslt_title = 'NFASLT Implementation' if nfaslt_success else 'NFASLT Implementation (Failed)'
ax3.set_title(nfaslt_title)
ax3.set_xlabel('Time (s)')
ax3.set_ylabel('Frequency (Hz)')
plt.colorbar(im3, ax=ax3, label='Magnitude')

# Difference between FASLT and ASLT
ax4 = axes[1, 1]
diff_fa = np.abs(spectrum_faslt) - np.abs(spectrum_aslt)
im4 = ax4.imshow(diff_fa, aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                 cmap='RdBu_r', vmin=-np.max(np.abs(diff_fa)), vmax=np.max(np.abs(diff_fa)))
ax4.set_title('Difference (FASLT - ASLT)')
ax4.set_xlabel('Time (s)')
ax4.set_ylabel('Frequency (Hz)')
plt.colorbar(im4, ax=ax4, label='Difference')

plt.suptitle(f'Implementation Comparison (c1={c1_comp2}, orders={order_comp2})', fontsize=14)
plt.tight_layout()
plt.savefig('figures/faslt_implementation_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 5: Parameter exploration - different base cycles
print("\n5. Parameter exploration - base cycles...")

# Different base cycles to test
c1_values = [1, 3, 5, 10]
order_range_param = [1, 20.5]  # Fractional order range

fig, axes = plt.subplots(2, 2, figsize=(10, 6), dpi=120)
axes = axes.flatten()

for i, c1_val in enumerate(c1_values):
    print(f"   Testing c1={c1_val}...")

    spectrum = faslt(signal, fs, freqs, c1_val, order_range_param, 0)

    im = axes[i].imshow(np.abs(spectrum), aspect='auto', origin='lower',
                        extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                        cmap='viridis')
    axes[i].set_xlabel('Time (s)')
    axes[i].set_ylabel('Frequency (Hz)')
    axes[i].set_title(f'c1={c1_val}, orders={order_range_param[0]}-{order_range_param[1]}')
    plt.colorbar(im, ax=axes[i], label='Magnitude')

plt.suptitle('Parameter Exploration: Base Cycles (FASLT)', fontsize=14)
plt.tight_layout()
plt.savefig('figures/faslt_parameter_exploration.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 6: Fractional vs Integer order comparison
print("\n6. Fractional vs Integer order comparison...")

# Compare fractional and integer orders
c1_frac = 5
order_int = [1, 15]      # Integer orders
order_frac = [1, 15.7]   # Fractional orders

fig, axes = plt.subplots(1, 3, figsize=(12, 4), dpi=120)

# Integer orders
print("   Integer orders...")
spectrum_int = faslt(signal, fs, freqs, c1_frac, order_int, 0)

# Fractional orders
print("   Fractional orders...")
spectrum_frac = faslt(signal, fs, freqs, c1_frac, order_frac, 0)

# Plot integer orders
im1 = axes[0].imshow(np.abs(spectrum_int), aspect='auto', origin='lower',
                     extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                     cmap='viridis')
axes[0].set_title(f'Integer Orders {order_int}')
axes[0].set_xlabel('Time (s)')
axes[0].set_ylabel('Frequency (Hz)')
plt.colorbar(im1, ax=axes[0], label='Magnitude')

# Plot fractional orders
im2 = axes[1].imshow(np.abs(spectrum_frac), aspect='auto', origin='lower',
                     extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                     cmap='viridis')
axes[1].set_title(f'Fractional Orders {order_frac}')
axes[1].set_xlabel('Time (s)')
axes[1].set_ylabel('Frequency (Hz)')
plt.colorbar(im2, ax=axes[1], label='Magnitude')

# Difference plot
diff_frac = np.abs(spectrum_frac) - np.abs(spectrum_int)
im3 = axes[2].imshow(diff_frac, aspect='auto', origin='lower',
                     extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                     cmap='RdBu_r', vmin=-np.max(np.abs(diff_frac)), vmax=np.max(np.abs(diff_frac)))
axes[2].set_title('Difference (Fractional - Integer)')
axes[2].set_xlabel('Time (s)')
axes[2].set_ylabel('Frequency (Hz)')
plt.colorbar(im3, ax=axes[2], label='Difference')

plt.suptitle(f'Fractional vs Integer Order Comparison (c1={c1_frac})', fontsize=14)
plt.tight_layout()
plt.savefig('figures/faslt_fractional_vs_integer.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 7: Performance comparison
print("\n7. Performance comparison...")

# Test with different signal lengths
lengths = [1000, 5000, 10000, 20000]
c1_perf = 3
order_perf = [1, 10.5]  # Fractional order

performance_results = []

for length in lengths:
    # Create test signal
    test_signal = np.random.randn(length)

    # FASLT timing
    start_time = time.time()
    _ = faslt(test_signal, fs, freqs[:20], c1_perf, order_perf, 0)
    time_faslt = time.time() - start_time

    # ASLT timing for comparison
    if aslt_available:
        try:
            start_time = time.time()
            _ = aslt(test_signal, fs, freqs[:20], c1_perf, [1, 10], False)  # Integer orders
            time_aslt = time.time() - start_time
        except Exception:
            time_aslt = float('inf')  # Mark as failed
    else:
        time_aslt = float('inf')  # Not available

    performance_results.append({
        'length': length,
        'faslt': time_faslt,
        'aslt': time_aslt,
        'ratio': time_faslt / time_aslt
    })

    print(f"   Length {length}: FASLT={time_faslt:.3f}s, ASLT={time_aslt:.3f}s, ratio={time_faslt/time_aslt:.2f}")

# Plot performance results
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(8, 4), dpi=120)

lengths_plot = [r['length'] for r in performance_results]
times_faslt = [r['faslt'] for r in performance_results]
times_aslt = [r['aslt'] for r in performance_results]
ratios = [r['ratio'] for r in performance_results]

ax1.plot(lengths_plot, times_faslt, 'o-', label='FASLT', linewidth=2)
ax1.plot(lengths_plot, times_aslt, 's-', label='ASLT', linewidth=2)
ax1.set_xlabel('Signal Length')
ax1.set_ylabel('Time (s)')
ax1.set_title('Execution Time Comparison')
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_xscale('log')
ax1.set_yscale('log')

ax2.plot(lengths_plot, ratios, 'o-', color='red', linewidth=2)
ax2.axhline(y=1, color='black', linestyle='--', alpha=0.5)
ax2.set_xlabel('Signal Length')
ax2.set_ylabel('Speed Ratio (FASLT / ASLT)')
ax2.set_title('Relative Performance')
ax2.grid(True, alpha=0.3)
ax2.set_xscale('log')

plt.tight_layout()
plt.savefig('figures/faslt_performance_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# Summary
print("\n" + "="*60)
print("FASLT TEST SUMMARY")
print("="*60)
print(f"Signal length: {len(signal)} samples")
print(f"Sampling rate: {fs} Hz")
print(f"Frequency range: {freqs[0]}-{freqs[-1]} Hz")
print(f"Number of frequencies: {len(freqs)}")
print("\nKey Features Demonstrated:")
print("   - Fractional superresolution orders (e.g., 1-15.7)")
print("   - Additive vs multiplicative superresolution")
print("   - Comparison with ASLT and NFASLT implementations")
print("   - Parameter exploration with different base cycles")
print("   - Performance benchmarking")
print("\nGenerated figures:")
print("   - figures/faslt_signal.png")
print("   - figures/faslt_wavelet_basic.png")
print("   - figures/faslt_fractional_orders.png")
print("   - figures/faslt_superresolution_comparison.png")
print("   - figures/faslt_implementation_comparison.png")
print("   - figures/faslt_parameter_exploration.png")
print("   - figures/faslt_fractional_vs_integer.png")
print("   - figures/faslt_performance_comparison.png")

print("\nPerformance summary:")
for result in performance_results:
    print(f"   Length {result['length']}: FASLT={result['faslt']:.3f}s vs ASLT={result['aslt']:.3f}s")

print("\nFASLT Advantages:")
print("   - Supports fractional superresolution orders")
print("   - Enhanced flexibility in time-frequency analysis")
print("   - Smooth interpolation between integer orders")
print("   - Compatible with both additive and multiplicative modes")

print("\nTesting completed successfully!")
print("The FASLT implementation provides enhanced capabilities over integer-only approaches.")
print("Fractional orders allow for finer control of the time-frequency trade-off.")
