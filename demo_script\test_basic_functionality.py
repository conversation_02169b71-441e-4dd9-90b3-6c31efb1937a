#!/usr/bin/env python3
"""
Basic functionality test for superlet and dlogst
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

try:
    # Try direct imports first
    from superlet import superlets as superlet_original
    from superletcx import superlets as superlet_cx
except ImportError:
    try:
        # Try package-style imports
        from code.superlet import superlets as superlet_original
        from code.superletcx import superlets as superlet_cx
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Make sure you're running this script from the demo_script directory")
        print("and that the code directory contains superlet.py and superletcx.py")
        print(f"Code directory: {code_dir}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

def test_imports():
    """Test if all required modules can be imported."""
    try:
        import numpy as np
        print("✓ numpy imported successfully")
        
        import matplotlib.pyplot as plt
        print("✓ matplotlib imported successfully")
        
        from superletcx import superlets
        print("✓ superletcx imported successfully")
        
        from reference.dlogst_spec_descriptor import dlogst_spec_descriptor
        print("✓ dlogst_spec_descriptor imported successfully")
        
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_basic_superlet():
    """Test basic superlet functionality."""
    try:
        from superletcx import superlets
        
        # Generate simple test signal
        fs = 1000
        t = np.linspace(0, 1, fs)
        signal = np.sin(2 * np.pi * 20 * t)  # 20 Hz sine wave
        
        # Superlet parameters
        freqs = np.linspace(10, 50, 20)
        c1 = 3
        orders = (1, 5)
        
        # Compute superlet transform
        spectrum = superlets(signal, fs, freqs, c1, orders)
        
        print(f"✓ Superlet transform computed successfully")
        print(f"  Signal shape: {signal.shape}")
        print(f"  Spectrum shape: {spectrum.shape}")
        print(f"  Frequency range: {freqs[0]:.1f} - {freqs[-1]:.1f} Hz")
        
        return True
    except Exception as e:
        print(f"✗ Superlet test failed: {e}")
        return False

def test_basic_dlogst():
    """Test basic DLOGST functionality."""
    try:
        from reference.dlogst_spec_descriptor import dlogst_spec_descriptor
        
        # Generate simple test signal
        fs = 1000
        dt = 1.0 / fs
        t = np.linspace(0, 1, fs)
        signal = np.sin(2 * np.pi * 20 * t)  # 20 Hz sine wave
        
        # DLOGST parameters
        fmax = 100
        shape = 0.35
        kmax = 120
        int_val = 35
        
        # Compute DLOGST
        MST, mag, phase, voice, peak_freq, freq_loc, spec_centroid, \
        spec_slope, mag_voice_slope, voice_slope, spec_decrease, time_dlogst, freqst = \
            dlogst_spec_descriptor(signal, dt, fmax, shape, kmax, int_val)
        
        print(f"✓ DLOGST computed successfully")
        print(f"  Signal shape: {signal.shape}")
        print(f"  MST shape: {MST.shape}")
        print(f"  Magnitude shape: {mag.shape}")
        print(f"  Frequency range: {freqst[0]:.1f} - {freqst[-1]:.1f} Hz")
        
        return True
    except Exception as e:
        print(f"✗ DLOGST test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing basic functionality...")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("Import tests failed. Cannot proceed.")
        return
    
    print("\n" + "=" * 50)
    
    # Test superlet
    if not test_basic_superlet():
        print("Superlet test failed.")
        return
    
    print("\n" + "=" * 50)
    
    # Test DLOGST
    if not test_basic_dlogst():
        print("DLOGST test failed.")
        return
    
    print("\n" + "=" * 50)
    print("✓ All basic functionality tests passed!")
    print("You can now run the comparison scripts.")

if __name__ == "__main__":
    main()
