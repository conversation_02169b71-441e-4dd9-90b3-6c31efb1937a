# -*- coding: utf-8 -*-
"""
Created on Tue Oct 22 17:01:49 2024

@author: devri.agustianto
"""
import numpy as np
from scipy.interpolate import interp1d

def fgabor(trin, t, twin, tinc, p=1, gdb=60, normflag=1, pow2option=1):
    """
    Forward Gabor transform with Gaussian analysis windowing.
    
    Parameters:
    -----------
    trin : array_like
        Input trace
    t : array_like
        Time coordinate vector for trin
    twin : float
        Half-width (seconds) of the Gaussian window
    tinc : float
        Temporal shift (seconds) between windows
    p : float, optional
        Exponent used in analysis windowing (default=1)
    gdb : float, optional
        Decibels below 1 for window truncation (default=60)
    normflag : int, optional
        Normalization flag (default=1)
    pow2option : int, optional
        Power of 2 expansion option (default=1)
    
    Returns:
    --------
    tvs : ndarray
        Complex-valued time-variant spectrum (Gabor spectrum)
    tout : ndarray
        Row coordinate of tvs
    fout : ndarray
        Column coordinate of tvs
    normf_tout : ndarray
        Gabor normalization values at window center times
    """
    dt = t[1] - t[0]
    n = len(t)
    
    # Calculate number of windows
    n_windows = int(np.floor((t[-1] - t[0] - 2*twin) / tinc)) + 1
    
    # Initialize output arrays
    tout = np.arange(n_windows) * tinc + t[0] + twin
    
    # Apply Gabor transform
    for i in range(n_windows):
        t_center = tout[i]
        t_indices = np.logical_and(t >= t_center-twin, t <= t_center+twin)
        segment_length = np.sum(t_indices)
        
        if segment_length > 0:
            # Create Gaussian window matching the segment length
            t_segment = t[t_indices]
            t_window = t_segment - t_center
            sigma = twin / np.sqrt(2)
            window = np.exp(-t_window**2 / (2 * sigma**2))
            
            # Window the data
            windowed = trin[t_indices] * window
            
            # Calculate FFT length
            if i == 0:
                nfft = segment_length
                if pow2option:
                    nfft = int(2**np.ceil(np.log2(nfft)))
                fout = np.fft.rfftfreq(nfft, dt)
                tvs = np.zeros((n_windows, len(fout)), dtype=complex)
            
            # Pad if necessary and apply FFT
            if len(windowed) < nfft:
                windowed = np.pad(windowed, (0, nfft-len(windowed)))
            tvs[i, :] = np.fft.rfft(windowed, n=nfft)
    
    # Normalize if requested
    normf_tout = np.ones(n_windows) if normflag else np.zeros(n_windows)
    tvs = tvs.T
    
    return tvs, tout, fout, normf_tout

def gaborfreq(s, t, twin, tinc, p=2):
    """
    Calculate local dominant frequency using the Gabor transform.
    
    Parameters:
    -----------
    s : array_like
        Input signal
    t : array_like
        Time coordinate vector for s in seconds
    twin : float
        Gaussian window half width in seconds
    tinc : float
        Increment between Gaussians in seconds
    p : float, optional
        Spectral exponent for calculating local frequency (default=2)
    
    Returns:
    --------
    freqloc : ndarray
        Local frequency computed from Gabor slices
    """
    # Compute Gabor transform
    S, trow, fcol, _ = fgabor(s, t, twin, tinc)
    
    # Calculate dominant frequency for each time slice
    fdom = np.zeros(len(trow))
    for k in range(len(trow)):
        spec = np.abs(S[:, k]) ** p
        fdom[k] = np.sum(fcol * spec) / np.sum(spec)
    
    # Interpolate back to original time grid
    interpolator = interp1d(trow, fdom, kind='linear', bounds_error=False, fill_value='extrapolate')
    freqloc = interpolator(t)
    
    return freqloc