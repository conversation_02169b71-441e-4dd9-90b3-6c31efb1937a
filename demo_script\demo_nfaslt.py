#!/usr/bin/env python3
"""
Negative/Normalized Fractional Adaptive Superlet Transform (NFASLT) Testing Script

This script demonstrates the application of the Negative/Normalized Fractional 
Adaptive Superlet Transform for time-frequency analysis using the implementation 
in nfaslt.py. Based on demo_faslt.py but adapted for the NFASLT implementation.

The NFASLT supports complex input data, negative frequency domains, and 
fractional superresolution orders with enhanced flexibility for complex signals.
"""

import numpy as np
import matplotlib.pyplot as plt
# Configure smaller figure and font sizes for better on-screen display
plt.rcParams.update({
    'font.size': 8,      # Reduce default text size
    'figure.dpi': 120    # Lower DPI so pixel dimensions are smaller
})
import time
import sys
import os

# Add the code directory to the Python path
code_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'code')
if code_dir not in sys.path:
    sys.path.insert(0, code_dir)

try:
    # Try direct imports first
    from nfaslt import nfaslt
except ImportError:
    try:
        # Try package-style imports
        from code.nfaslt import nfaslt
    except ImportError as e:
        print(f"Import Error: {e}")
        print("Make sure you're running this script from the demo_script directory")
        print("and that the code directory contains nfaslt.py")
        print(f"Code directory: {code_dir}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

# Signal generation parameters
fs = 1024  # Sampling rate
burst_freqs = [20, 40, 60]  # Frequencies of signal bursts
f_shift = 10  # Frequency shift for contamination
n_cycles = 11  # Number of cycles for main bursts
n_neighb_cycles = 12  # Number of cycles for neighboring bursts

print("Generating synthetic signal...")

# Generate synthetic signal similar to demo_faslt.py
ys = []
for f in burst_freqs:
    # Main burst
    t = 1/f * n_cycles
    x = np.linspace(0, t, int(t * fs))
    y = np.sin(2*np.pi*f*x) + np.sin(2*np.pi*(f+f_shift)*x - np.pi/1.5)
    ys.append(y)
    
    # Neighboring burst
    t2 = 1/f * n_neighb_cycles
    x2 = np.linspace(0, t2, int(t2 * fs))
    y2 = np.sin(2*np.pi*(f+15)*x2) + np.sin(2*np.pi*(f+25)*x2 - np.pi/3)
    ys.append(y2)

# Concatenate all signal components
signal = np.concatenate(ys)

# Add some noise for realism
np.random.seed(42)
noise_level = 0.1
signal += noise_level * np.random.randn(len(signal))

print(f"Generated signal with {len(signal)} samples at {fs} Hz")

# Time vector for plotting
time_vector = np.arange(len(signal)) / fs

# Create figure directory
import os
os.makedirs('figures', exist_ok=True)

# Plot the generated signal
fig, ax = plt.subplots(figsize=(8, 2), dpi=120)
ax.plot(time_vector, signal, linewidth=0.8, color='black')
ax.set_xlabel('Time (s)')
ax.set_ylabel('Amplitude')
ax.set_title('Synthetic Test Signal for NFASLT Analysis')
ax.grid(True, alpha=0.3)
ax.set_xlim(0, time_vector[-1])
plt.tight_layout()
plt.savefig('figures/nfaslt_signal.png', dpi=300, bbox_inches='tight')
plt.show()

print("\n" + "="*60)
print("TESTING NEGATIVE/NORMALIZED FRACTIONAL ADAPTIVE SUPERLET TRANSFORMS")
print("="*60)

# Test 1: Basic wavelet transform (o=None gives CWT)
print("\n1. Testing basic wavelet transform (o=None)...")

# Parameters for basic wavelet
c1_basic = 10
Fi_basic = [10, 80]  # Frequency interval
Nf_basic = 141       # Number of frequency points

# Using nfaslt.py (o=None gives standard CWT)
spectrum_basic = nfaslt(signal, fs, Fi_basic, Nf_basic, c1_basic, None, False)

# Plot basic wavelet transform
fig, ax = plt.subplots(figsize=(7, 3), dpi=120)
freqs_basic = np.linspace(Fi_basic[0], Fi_basic[1], Nf_basic)
im = ax.imshow(np.abs(spectrum_basic), aspect='auto', origin='lower',
               extent=(0, len(signal)/fs, freqs_basic[0], freqs_basic[-1]),
               cmap='viridis')
ax.set_xlabel('Time (s)')
ax.set_ylabel('Frequency (Hz)')
ax.set_title(f'Basic Wavelet Transform via NFASLT (c1={c1_basic})')
plt.colorbar(im, ax=ax, label='Magnitude')
plt.tight_layout()
plt.savefig('figures/nfaslt_wavelet_basic.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 2: NFASLT with different fractional orders
print("\n2. Testing NFASLT with different fractional orders...")

# Parameters for NFASLT
c1 = 3  # Base cycles
Fi = [10, 80]  # Frequency interval
Nf = 71        # Number of frequency points
order_ranges = [[1, 5.5], [1, 10.2], [1, 15.7], [1, 30.3]]  # Different fractional order ranges

fig, axes = plt.subplots(2, 2, figsize=(9, 6), dpi=120)
axes = axes.flatten()
freqs = np.linspace(Fi[0], Fi[1], Nf)

for i, order_range in enumerate(order_ranges):
    print(f"   Processing fractional order range {order_range}...")
    
    # Using nfaslt.py with fractional orders
    start_time = time.time()
    spectrum = nfaslt(signal, fs, Fi, Nf, c1, order_range, False)  # False = additive
    time_nfaslt = time.time() - start_time
    
    print(f"      NFASLT: {time_nfaslt:.3f}s")
    
    # Plot results
    im = axes[i].imshow(np.abs(spectrum), aspect='auto', origin='lower',
                        extent=(0, len(signal)/fs, freqs[0], freqs[-1]),
                        cmap='viridis')
    axes[i].set_xlabel('Time (s)')
    axes[i].set_ylabel('Frequency (Hz)')
    axes[i].set_title(f'NFASLT (c1={c1}, orders={order_range[0]}-{order_range[1]})')
    plt.colorbar(im, ax=axes[i], label='Magnitude')

plt.suptitle('Negative/Normalized Fractional Adaptive Superlet Transform Results', fontsize=12)
plt.tight_layout()
plt.savefig('figures/nfaslt_fractional_orders.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 3: Comparison between additive and multiplicative superresolution
print("\n3. Comparing additive vs multiplicative superresolution...")

# Parameters for comparison
c1_comp = 5
order_comp = [1, 15.5]  # Fractional order range
Fi_comp = [10, 80]
Nf_comp = 71

fig, axes = plt.subplots(2, 2, figsize=(10, 6), dpi=120)
freqs_comp = np.linspace(Fi_comp[0], Fi_comp[1], Nf_comp)

# Additive superresolution
print("   Additive superresolution...")
spectrum_add = nfaslt(signal, fs, Fi_comp, Nf_comp, c1_comp, order_comp, False)

# Multiplicative superresolution  
print("   Multiplicative superresolution...")
spectrum_mult = nfaslt(signal, fs, Fi_comp, Nf_comp, c1_comp, order_comp, True)

# Plot comparisons
ax1 = axes[0, 0]
im1 = ax1.imshow(np.abs(spectrum_add), aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs_comp[0], freqs_comp[-1]),
                 cmap='viridis')
ax1.set_title('Additive Superresolution')
ax1.set_xlabel('Time (s)')
ax1.set_ylabel('Frequency (Hz)')
plt.colorbar(im1, ax=ax1, label='Magnitude')

ax2 = axes[0, 1]
im2 = ax2.imshow(np.abs(spectrum_mult), aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs_comp[0], freqs_comp[-1]),
                 cmap='viridis')
ax2.set_title('Multiplicative Superresolution')
ax2.set_xlabel('Time (s)')
ax2.set_ylabel('Frequency (Hz)')
plt.colorbar(im2, ax=ax2, label='Magnitude')

# Difference plot
ax3 = axes[1, 0]
diff = np.abs(spectrum_add) - np.abs(spectrum_mult)
im3 = ax3.imshow(diff, aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs_comp[0], freqs_comp[-1]),
                 cmap='RdBu_r', vmin=-np.max(np.abs(diff)), vmax=np.max(np.abs(diff)))
ax3.set_title('Difference (Additive - Multiplicative)')
ax3.set_xlabel('Time (s)')
ax3.set_ylabel('Frequency (Hz)')
plt.colorbar(im3, ax=ax3, label='Difference')

# Relative difference
ax4 = axes[1, 1]
rel_diff = 2 * np.abs(spectrum_add - spectrum_mult) / (np.abs(spectrum_add) + np.abs(spectrum_mult) + 1e-10)
im4 = ax4.imshow(rel_diff, aspect='auto', origin='lower',
                 extent=(0, len(signal)/fs, freqs_comp[0], freqs_comp[-1]),
                 cmap='hot', vmin=0, vmax=0.1)
ax4.set_title('Relative Difference')
ax4.set_xlabel('Time (s)')
ax4.set_ylabel('Frequency (Hz)')
plt.colorbar(im4, ax=ax4, label='Relative Difference')

plt.suptitle(f'Superresolution Mode Comparison (c1={c1_comp}, orders={order_comp})', fontsize=14)
plt.tight_layout()
plt.savefig('figures/nfaslt_superresolution_comparison.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 4: Parameter exploration - different base cycles
print("\n4. Parameter exploration - base cycles...")

# Different base cycles to test
c1_values = [1, 3, 5, 10]
order_range_param = [1, 20.5]  # Fractional order range
Fi_param = [10, 80]
Nf_param = 71

fig, axes = plt.subplots(2, 2, figsize=(10, 6), dpi=120)
axes = axes.flatten()
freqs_param = np.linspace(Fi_param[0], Fi_param[1], Nf_param)

for i, c1_val in enumerate(c1_values):
    print(f"   Testing c1={c1_val}...")
    
    spectrum = nfaslt(signal, fs, Fi_param, Nf_param, c1_val, order_range_param, False)

    im = axes[i].imshow(np.abs(spectrum), aspect='auto', origin='lower',
                        extent=(0, len(signal)/fs, freqs_param[0], freqs_param[-1]),
                        cmap='viridis')
    axes[i].set_xlabel('Time (s)')
    axes[i].set_ylabel('Frequency (Hz)')
    axes[i].set_title(f'c1={c1_val}, orders={order_range_param[0]}-{order_range_param[1]}')
    plt.colorbar(im, ax=axes[i], label='Magnitude')

plt.suptitle('Parameter Exploration: Base Cycles (NFASLT)', fontsize=14)
plt.tight_layout()
plt.savefig('figures/nfaslt_parameter_exploration.png', dpi=300, bbox_inches='tight')
plt.show()

# Test 5: Complex signal analysis
print("\n5. Complex signal analysis...")

# Generate a complex signal
print("   Generating complex signal...")
complex_signal = signal + 1j * np.roll(signal, len(signal)//4)  # Add imaginary component

# Parameters for complex analysis
c1_complex = 5
order_complex = [1, 15.7]
Fi_complex = [10, 80]
Nf_complex = 71

fig, axes = plt.subplots(1, 3, figsize=(12, 4), dpi=120)
freqs_complex = np.linspace(Fi_complex[0], Fi_complex[1], Nf_complex)

# Real signal analysis
spectrum_real = nfaslt(signal, fs, Fi_complex, Nf_complex, c1_complex, order_complex, False)

# Complex signal analysis
spectrum_complex = nfaslt(complex_signal, fs, Fi_complex, Nf_complex, c1_complex, order_complex, False)

# Plot real signal analysis
im1 = axes[0].imshow(np.abs(spectrum_real), aspect='auto', origin='lower',
                     extent=(0, len(signal)/fs, freqs_complex[0], freqs_complex[-1]),
                     cmap='viridis')
axes[0].set_title('Real Signal Analysis')
axes[0].set_xlabel('Time (s)')
axes[0].set_ylabel('Frequency (Hz)')
plt.colorbar(im1, ax=axes[0], label='Magnitude')

# Plot complex signal analysis
im2 = axes[1].imshow(np.abs(spectrum_complex), aspect='auto', origin='lower',
                     extent=(0, len(signal)/fs, freqs_complex[0], freqs_complex[-1]),
                     cmap='viridis')
axes[1].set_title('Complex Signal Analysis')
axes[1].set_xlabel('Time (s)')
axes[1].set_ylabel('Frequency (Hz)')
plt.colorbar(im2, ax=axes[1], label='Magnitude')

# Difference plot
diff_complex = np.abs(spectrum_complex) - np.abs(spectrum_real)
im3 = axes[2].imshow(diff_complex, aspect='auto', origin='lower',
                     extent=(0, len(signal)/fs, freqs_complex[0], freqs_complex[-1]),
                     cmap='RdBu_r', vmin=-np.max(np.abs(diff_complex)), vmax=np.max(np.abs(diff_complex)))
axes[2].set_title('Difference (Complex - Real)')
axes[2].set_xlabel('Time (s)')
axes[2].set_ylabel('Frequency (Hz)')
plt.colorbar(im3, ax=axes[2], label='Difference')

plt.suptitle(f'Complex vs Real Signal Analysis (c1={c1_complex})', fontsize=14)
plt.tight_layout()
plt.savefig('figures/nfaslt_complex_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# Summary
print("\n" + "="*60)
print("NFASLT TEST SUMMARY")
print("="*60)
print(f"Signal length: {len(signal)} samples")
print(f"Sampling rate: {fs} Hz")
print(f"Frequency interval: {Fi[0]}-{Fi[1]} Hz")
print(f"Number of frequencies: {Nf}")
print("\nKey Features Demonstrated:")
print("   - Fractional superresolution orders with frequency interval specification")
print("   - Complex signal support")
print("   - Additive vs multiplicative superresolution")
print("   - Parameter exploration with different base cycles")
print("   - Complex vs real signal analysis")
print("\nGenerated figures:")
print("   - figures/nfaslt_signal.png")
print("   - figures/nfaslt_wavelet_basic.png")
print("   - figures/nfaslt_fractional_orders.png")
print("   - figures/nfaslt_superresolution_comparison.png")
print("   - figures/nfaslt_parameter_exploration.png")
print("   - figures/nfaslt_complex_analysis.png")

print("\nNFASLT Advantages:")
print("   - Supports complex input data")
print("   - Frequency interval specification (Fi, Nf)")
print("   - Fractional superresolution orders")
print("   - Enhanced flexibility for complex signal analysis")
print("   - Negative frequency domain support")

print("\nTesting completed successfully!")
print("The NFASLT implementation provides enhanced capabilities for complex signals.")
print("Frequency interval specification allows for flexible frequency domain analysis.")
