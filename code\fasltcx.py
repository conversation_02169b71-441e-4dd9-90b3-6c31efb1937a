def fasltcx(input_data, Fs, F, c1, o, mult):
    """
    Modified faslt to output complex spectrum.
    """
    if len(F) == 0:
        raise ValueError('frequencies not defined')
    
    if input_data.size == 0:
        raise ValueError('input is empty')
    
    # Handle input shape
    if input_data.ndim == 1:
        input_data = input_data.reshape(1, -1)
    elif input_data.ndim > 2:
        raise ValueError('Input must be 1D or 2D')
    
    Nbuffers, Npoints = input_data.shape
    
    # Handle order parameter
    if o is None or len(o) == 0:
        order_frac = np.ones(len(F))
        order_int = order_frac
    else:
        if len(o) != 2:
            raise ValueError('Order interval must be a 2-element array')
        order_frac = np.linspace(o[0], o[1], len(F))
        order_int = np.ceil(order_frac)
    
    # Create wavelet sets
    wavelets = [[] for _ in range(len(F))]
    padding = 0
    
    for i_freq in range(len(F)):
        for i_ord in range(1, int(order_int[i_freq]) + 1):
            if mult != 0:
                n_cyc = i_ord * c1
            else:
                n_cyc = i_ord + c1
            
            wavelet = cxmorlet(F[i_freq], n_cyc, Fs)
            wavelets[i_freq].append(wavelet)
            
            padding = max(padding, int(np.fix(len(wavelet) / 2)))

    # Buffers
    buffer = np.zeros(int(Npoints + 2 * padding), dtype=np.complex128)
    wtresult = np.zeros((len(F), Npoints), dtype=np.complex128)

    bufbegin = int(padding)
    bufend = int(padding + Npoints)
    
    # Loop over input buffers
    for i_buf in range(Nbuffers):
        for i_freq in range(len(F)):
            # Init magnitude and phase buffers
            mag_buffer = np.ones(Npoints, dtype=np.float64)
            phase_buffer = np.zeros(Npoints, dtype=np.complex128)
            
            # Fill central part of buffer
            buffer[bufbegin:bufend] = input_data[i_buf, :]
            
            # Number of integer wavelets
            n_wavelets = np.floor(order_frac[i_freq])
            
            # Convolve with each integer wavelet
            for i_ord in range(1, int(n_wavelets) + 1):
                conv = fftconvolve(buffer, wavelets[i_freq][i_ord - 1], mode='same')[bufbegin:bufend]
                abs_conv = np.abs(conv)
                mag_buffer *= abs_conv
                phase_buffer += conv / (abs_conv + 1e-10)  # Avoid division by zero
            
            # Handle fractional part
            if is_fractional(order_frac[i_freq]) and len(wavelets[i_freq]) > int(n_wavelets):
                i_ord = int(order_int[i_freq])
                exponent = order_frac[i_freq] - np.fix(order_frac[i_freq])
                
                conv = fftconvolve(buffer, wavelets[i_freq][i_ord - 1], mode='same')[bufbegin:bufend]
                abs_conv = np.abs(conv)
                mag_buffer *= abs_conv ** exponent
                phase_buffer += (conv / (abs_conv + 1e-10)) * exponent
            
            # Geometric mean for magnitude, arithmetic for phase
            rfactor = 1 / order_frac[i_freq]
            magnitude = mag_buffer ** rfactor
            phase = phase_buffer / order_frac[i_freq]
            
            # Combine to complex
            wtresult[i_freq, :] += magnitude * phase  # Phase is unit complex
    
    # Average over buffers
    wtresult /= Nbuffers
    
    return wtresult