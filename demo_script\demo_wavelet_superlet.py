import numpy as np
import pywt
import matplotlib.pyplot as plt

# Synthetic signal: 5 Hz continuous + 20 Hz burst from t=0.5 to 0.7 s
fs = 100.0  # sampling rate (Hz)
t = np.arange(0, 1.0, 1/fs)
sig_low = np.sin(2*np.pi*5.0*t)  # 5 Hz oscillation
sig_high = np.sin(2*np.pi*20.0*t)
# Window the 20 Hz component to make it a burst (use a tapered rectangular window)
burst_window = np.zeros_like(t)
burst_window[(t>=0.5) & (t<=0.7)] = 1.0
# Taper edges of the burst window for smoothness
edge = 0.02
rise = (t>=0.5) & (t<0.5+edge)
fall = (t>0.7-edge) & (t<=0.7)
burst_window[rise] = 0.5*(1 - np.cos(np.pi*(t[rise]-0.5)/edge))
burst_window[fall] = 0.5*(1 + np.cos(np.pi*(t[fall]-0.7)/edge))
sig_high *= burst_window
signal = sig_low + sig_high

# Define frequency range and corresponding wavelet scales
freqs = np.linspace(1, 30, 60)                # frequencies 1 to 30 Hz
scales = (fs / freqs)                         # scale = sampling_freq / freq, for Morlet (cmor) wavelet with center_freq=1
# CWT using a complex Morlet wavelet (cmor1.0-1.0 -> moderate 1.0 bandwidth, 1.0 center freq)
cwt_coeffs, cwt_freqs = pywt.cwt(signal, scales, wavelet='cmor1.0-1.0', sampling_period=1/fs)
# S-transform: implement by convolution for each frequency
S_mag = np.zeros((len(freqs), len(t)))  # to store |S(t,f)|
for i, f in enumerate(freqs):
    # Multiply signal by e^{-i 2π f t}
    y = signal * np.exp(-2j * np.pi * f * t)
    # Gaussian window for S-transform at freq f
    sigma = 1.0/(np.sqrt(np.pi)*f)
    # Discretize window: truncate at ±3σ (or ±6σ for more coverage)
    L = int(min(6*sigma*fs, len(t)//2))
    tau = np.arange(-L, L+1) / fs
    window = np.exp(-np.pi * (f**2) * tau**2)
    window *= (f * (1/fs))  # include |f| and dt in amplitude
    conv = np.convolve(y, window, mode='same')
    # Ensure the convolution result matches the signal length
    if len(conv) != len(t):
        # Trim or pad to match signal length
        if len(conv) > len(t):
            start = (len(conv) - len(t)) // 2
            conv = conv[start:start+len(t)]
        else:
            # This case shouldn't happen with 'same' mode, but handle just in case
            pad_left = (len(t) - len(conv)) // 2
            pad_right = len(t) - len(conv) - pad_left
            conv = np.pad(conv, (pad_left, pad_right), mode='constant')
    S_mag[i, :] = np.abs(conv)
# Superlet (order 2) using two Morlet wavelets of different cycle counts:
# Use cmor0.5-1.0 (narrow-band, long wavelet) and cmor2.0-1.0 (wide-band, short wavelet)
coeffs_long, _  = pywt.cwt(signal, scales, wavelet='cmor0.5-1.0', sampling_period=1/fs)
coeffs_short, _ = pywt.cwt(signal, scales, wavelet='cmor2.0-1.0', sampling_period=1/fs)
SL_mag = np.sqrt(np.abs(coeffs_long) * np.abs(coeffs_short))  # geometric mean magnitude

# Plot the scalograms
fig, axes = plt.subplots(3, 1, figsize=(8, 6), sharex=True)
# CWT Scalogram
axes[0].imshow(np.abs(cwt_coeffs), extent=[0, 1, freqs[0], freqs[-1]],
               origin='lower', aspect='auto')
axes[0].set_ylabel('Frequency [Hz]')
axes[0].set_title('CWT Scalogram (Morlet wavelet)')
# S-Transform magnitude
axes[1].imshow(S_mag, extent=[0, 1, freqs[0], freqs[-1]],
               origin='lower', aspect='auto')
axes[1].set_ylabel('Frequency [Hz]')
axes[1].set_title('S-Transform')
# Superlet (order 2) Scalogram
axes[2].imshow(SL_mag, extent=[0, 1, freqs[0], freqs[-1]],
               origin='lower', aspect='auto')
axes[2].set_xlabel('Time [sec]')
axes[2].set_ylabel('Frequency [Hz]')
axes[2].set_title('Superlet Transform (Order 2)')
plt.tight_layout()
plt.show()
