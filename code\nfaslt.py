import numpy as np

def nfaslt(input_data, Fs, Fi, Nf, c1, o=None, mult=False):
    """
    Computes the fractional adaptive superresolution wavelet (superlet) 
    transform on input data. Supports complex input and negative frequency 
    domains.

    REFERENCE:
    Time-frequency super-resolution with superlets
    Moca, V.V., Nagy-<PERSON>, A., Barzan, H., Muresan, R.C.
    https://www.nature.com/articles/s41467-020-20539-9

    Args:
        input_data (np.ndarray): A [buffers x samples] matrix (real or complex).
        Fs (float): The sampling frequency in Hz.
        Fi (list or tuple): A 2-element list [lower, upper] for the frequency interval.
        Nf (int): Number of frequency points in the interval Fi.
        c1 (float): Initial superlet number of cycles.
        o (list or tuple, optional): A 2-element list [lower, upper] for the 
                                     superresolution orders. Defaults to None (order=1).
        mult (bool, optional): Use multiplicative (True) or additive (False) 
                               superresolution. Defaults to False.

    Returns:
        np.ndarray: Real matrix [frequencies x samples] - the superlet spectrum.
    """
    # --- Input Validation and Frequency Setup ---
    if not Fi or len(Fi) != 2 or Nf < 1:
        raise ValueError("Bad frequency definition.")
    if Fi[0] > Fi[1]:
        Fi = [Fi[1], Fi[0]]
    F = np.linspace(Fi[0], Fi[1], Nf)

    # --- Order Parameter Setup ---
    if o is not None:
        if Fi[0] > 0:  # Positive-only frequency domain
            order_frac = np.linspace(o[0], o[1], Nf)
        elif Fi[1] < 0:  # Negative-only frequency domain
            order_frac = np.linspace(o[1], o[0], Nf)
        else:  # Frequency domain includes 0
            order_frac = np.zeros(Nf)
            for i in range(Nf):
                order_frac[i] = r2rmap(np.abs(F[i]), 0, Fi[1], o[0], o[1])
        order_int = np.ceil(order_frac).astype(int)
    else:
        order_frac = np.ones(len(F))
        order_int = np.ones(len(F), dtype=int)

    # --- Input Buffer Validation ---
    if input_data is None or input_data.size == 0:
        raise ValueError("Input data is empty.")
    if input_data.ndim == 1:
        input_data = input_data[np.newaxis, :]
    
    Nbuffers, Npoints = input_data.shape
    padding = 0

    # --- Generate Wavelet Sets ---
    max_order = np.max(order_int) if len(order_int) > 0 else 0
    wavelets = [[None for _ in range(max_order)] for _ in range(len(F))]

    for i_freq in range(len(F)):
        if np.abs(F[i_freq]) < 1e-10:
            continue
        for i_ord in range(1, order_int[i_freq] + 1):
            n_cyc = i_ord * c1 if mult else i_ord + c1 -1
            w = cxmorlet(-F[i_freq], n_cyc, Fs)
            wavelets[i_freq][i_ord - 1] = w
            if w is not None:
                padding = max(padding, int(len(w) / 2))

    # --- Main Computation ---
    buffer = np.zeros(Npoints + 2 * padding, dtype=input_data.dtype)
    wtresult = np.zeros((len(F), Npoints))
    
    bufbegin = padding
    bufend = padding + Npoints

    for i_buf in range(Nbuffers):
        for i_freq in range(len(F)):
            if np.abs(F[i_freq]) < 1e-10 or wavelets[i_freq][0] is None:
                avg = 2 * np.mean(np.abs(np.real(input_data[i_buf, :])))
                wtresult[i_freq, :] += avg
                continue

            temp = np.ones(Npoints)
            buffer[bufbegin:bufend] = input_data[i_buf, :]
            
            # Convolve with integer-order wavelets
            n_wavelets = int(np.floor(order_frac[i_freq]))
            for i_ord in range(n_wavelets):
                tempcx = np.convolve(buffer, wavelets[i_freq][i_ord], mode='same')
                temp *= (2 * np.abs(tempcx[bufbegin:bufend]))**2
            
            # Handle fractional part
            if is_fractional(order_frac[i_freq]):
                i_ord_frac = order_int[i_freq] - 1
                if i_ord_frac < len(wavelets[i_freq]) and wavelets[i_freq][i_ord_frac] is not None:
                    exponent = order_frac[i_freq] - np.floor(order_frac[i_freq])
                    tempcx = np.convolve(buffer, wavelets[i_freq][i_ord_frac], mode='same')
                    term = (2 * np.abs(tempcx[bufbegin:bufend]))**2
                    temp *= term ** exponent
            
            # Compute geometric mean and accumulate
            root = 1.0 / order_frac[i_freq]
            wtresult[i_freq, :] += temp ** root

    wtresult /= Nbuffers
    return wtresult

# --- Helper Functions ---

def cxmorlet(Fc, Nc, Fs):
    if np.abs(Fc) < 1e-10:
        return None
    sd = (Nc / 2) * abs(1 / Fc) / 2.5
    wl = 2 * np.floor(int(6 * sd * Fs) / 2) + 1
    w = np.zeros(int(wl), dtype=np.complex128)
    gi = 0
    off = int(wl / 2)

    for i in range(int(wl)):
        t = (i - off) / Fs
        w[i] = bw_cf(t, sd, Fc)
        gi += gauss(t, sd)
    return w / gi

def bw_cf(t, bw, cf):
    cnorm = 1 / (bw * np.sqrt(2 * np.pi))
    exp1 = cnorm * np.exp(-(t**2) / (2 * bw**2))
    return np.exp(2j * np.pi * cf * t) * exp1

def gauss(t, sd):
    cnorm = 1 / (sd * np.sqrt(2 * np.pi))
    return cnorm * np.exp(-(t**2) / (2 * sd**2))

def is_fractional(x):
    return x != np.floor(x)

def r2rmap(x, x1, x2, y1, y2):
    if (x2 - x1) == 0: return y1 # Avoid division by zero
    return y1 + (y2 - y1) / (x2 - x1) * (x - x1)