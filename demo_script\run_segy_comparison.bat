@echo off
REM Batch script to run the SEG-Y superlet vs DLOGST comparison scripts
REM This script uses the correct Python environment

echo ========================================
echo SEG-Y Superlet vs DLOGST Comparison
echo ========================================
echo.

echo Available SEG-Y comparison scripts:
echo 1. segy_comparison_simple.py - Simple version with default parameters
echo 2. segy_superlet_dlogst_comparison.py - Full version with GUI parameter selection
echo 3. Test basic functionality first
echo.

set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Running simple SEG-Y comparison...
    echo This will open a file dialog to select your SEG-Y file.
    echo Default trace indices: W_BTP=189, B_1=460, TN_S253=1432
    echo.
    C:\Users\<USER>\PrizmEnv\Scripts\python.exe segy_comparison_simple.py
) else if "%choice%"=="2" (
    echo Running full SEG-Y comparison with GUI...
    echo This will open dialogs for file selection and parameter adjustment.
    echo.
    C:\Users\<USER>\PrizmEnv\Scripts\python.exe segy_superlet_dlogst_comparison.py
) else if "%choice%"=="3" (
    echo Testing basic functionality...
    C:\Users\<USER>\PrizmEnv\Scripts\python.exe test_basic_functionality.py
) else (
    echo Invalid choice. Please run the script again.
)

echo.
echo Script completed. Check the generated PNG files for results.
echo Generated files will be named: segy_comparison_[TraceName].png
pause
