# SEG-Y Superlet vs DLOGST Comparison Scripts

This directory contains scripts for loading SEG-Y seismic data and creating comprehensive 7-column comparison plots between Superlet Transform and DLOGST (Discrete Logarithmic S-Transform) spectral descriptors.

## Overview

The SEG-Y comparison scripts combine the functionality from:
- `reference/ex_2nd_Local_fomel_freq_Seismic_Arbitrary.py` (SEG-Y loading)
- `demo_superlet_dlogst_comparison.py` (7-column comparison structure)
- `superletcx.py` (Superlet Transform)
- `reference/dlogst_spec_descriptor.py` (DLOGST analysis)

## Files Created

### Main SEG-Y Comparison Scripts

1. **`segy_comparison_simple.py`** ⭐ **Recommended for most users**
   - Streamlined version with sensible defaults
   - Minimal user interaction required
   - Uses predefined trace indices: W_BTP (189), B_1 (460), TN_S253 (1432)
   - Fixed parameters optimized for seismic data

2. **`segy_superlet_dlogst_comparison.py`**
   - Full-featured version with GUI parameter selection
   - Interactive trace selection
   - Customizable plot limits and transform parameters
   - More complex but highly configurable

### Utility Scripts

3. **`run_segy_comparison.bat`**
   - Windows batch script for easy execution
   - Menu-driven interface
   - Automatically uses correct Python environment

## 7-Column Plot Structure

Each generated plot contains:

1. **Column 1**: Original seismic signal (amplitude vs time)
2. **Column 2**: Superlet Magnitude spectrogram
3. **Column 3**: Superlet Voice (real component showing oscillations)
4. **Column 4**: Superlet Magnitude × Voice (combined amplitude-phase info)
5. **Column 5**: DLOGST Magnitude spectrogram
6. **Column 6**: DLOGST Voice (real component showing oscillations)
7. **Column 7**: DLOGST Magnitude × Voice (combined amplitude-phase info)

## Usage

### Method 1: Using Batch Script (Easiest)
```batch
run_segy_comparison.bat
```
Select option 1 for simple version or option 2 for full version.

### Method 2: Direct Execution
```batch
# Simple version (recommended)
C:\Users\<USER>\PrizmEnv\Scripts\python.exe segy_comparison_simple.py

# Full version with GUI
C:\Users\<USER>\PrizmEnv\Scripts\python.exe segy_superlet_dlogst_comparison.py
```

## Input Requirements

### SEG-Y File Format
- Standard SEG-Y format (.sgy files)
- The script reads:
  - Trace data using `segyio.tools.collect()`
  - Sampling interval from binary header
  - Automatically calculates time array

### Trace Indices
The scripts use predefined trace indices that correspond to specific seismic locations:
- **W_BTP**: Index 189
- **B_1**: Index 460  
- **TN_S253**: Index 1432

These can be modified in the script or selected interactively in the full version.

## Parameters

### Default Parameters (Simple Version)
```python
freq_range = (5, 100)      # Frequency range for analysis (Hz)
c1 = 3                     # Superlet base cycles
orders = (1, 10)           # Superlet orders (min, max)
shape = 0.35               # DLOGST shape parameter
kmax = 120                 # DLOGST kmax parameter
int_val = 35               # DLOGST intercept parameter
colormap = 'viridis'       # Colormap for spectrograms
```

### Customizable Parameters (Full Version)
- Plot limits for all displays
- Transform parameters for both methods
- Colormap selection
- Trace selection interface

## Output

### Generated Files
Each processed trace generates a high-resolution PNG file:
- `segy_comparison_W_BTP.png`
- `segy_comparison_B_1.png`
- `segy_comparison_TN_S253.png`

### File Specifications
- Resolution: 300 DPI
- Format: PNG
- Size: 28×10 inches (optimized for detailed analysis)
- Time axis: Inverted (seismic convention - time increases downward)

## Features

### Data Validation
- Automatic validation of trace indices against available data
- Graceful handling of missing or invalid traces
- Informative error messages and warnings

### Visualization Features
- Synchronized time axes across all columns
- Consistent frequency ranges for direct comparison
- Appropriate colormaps (viridis for magnitude, RdBu for voice)
- Individual colorbars for each spectrogram
- Seismic convention time axis (increasing downward)

### MultiCursor Support (Full Version)
- Synchronized cursor across all plots
- Enables precise comparison between methods
- Works across multiple trace plots

## Technical Details

### Frequency Analysis
- **Superlet**: Uses frequency array from 5-100 Hz (80 points)
- **DLOGST**: Computed up to Nyquist frequency, then limited to match Superlet range
- Both methods use the same time array from SEG-Y data

### Voice Component
- **Superlet Voice**: Real part of complex superlet spectrum
- **DLOGST Voice**: Real part of complex DLOGST spectrum
- Shows oscillatory behavior and phase information

### Magnitude × Voice
- Combines amplitude and phase information
- Highlights regions of strong coherent signal
- Useful for identifying seismic events and noise

## Troubleshooting

### Common Issues

1. **"No file selected" Error**
   - Ensure you select a valid SEG-Y file in the dialog
   - Check file permissions and path

2. **"Trace index exceeds available traces" Warning**
   - Your SEG-Y file has fewer traces than expected
   - Modify trace indices in the script or use interactive selection

3. **Import Errors**
   - Run `test_basic_functionality.py` first
   - Ensure PrizmEnv environment is properly set up

4. **Memory Issues**
   - Large SEG-Y files may require significant memory
   - Consider reducing frequency resolution or processing fewer traces

### Performance Tips
- Simple version is faster for routine analysis
- Full version provides more control but takes longer
- Processing time depends on trace length and frequency resolution

## Customization

### Modifying Trace Indices
Edit the `trace_indices` dictionary in `segy_comparison_simple.py`:
```python
trace_indices = {
    'YourLocation1': 100,
    'YourLocation2': 250,
    'YourLocation3': 500
}
```

### Adjusting Parameters
Modify the parameter section in `segy_comparison_simple.py`:
```python
freq_range = (10, 80)      # Different frequency range
c1 = 5                     # More cycles for better frequency resolution
orders = (1, 15)           # Higher order for better time resolution
```

## Integration with Existing Workflow

These scripts are designed to integrate with the existing seismic analysis workflow:
- Compatible with standard SEG-Y files
- Uses same trace indexing as `ex_2nd_Local_fomel_freq_Seismic_Arbitrary.py`
- Maintains consistent parameter naming and structure
- Output format suitable for further analysis or reporting

## References

Based on the following source files:
- `reference/ex_2nd_Local_fomel_freq_Seismic_Arbitrary.py`
- `demo_superlet_dlogst_comparison.py`
- `superletcx.py`
- `reference/dlogst_spec_descriptor.py`
