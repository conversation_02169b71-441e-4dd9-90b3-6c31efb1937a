# -*- coding: utf-8 -*-
"""
Created on Tue Oct 22 17:25:53 2024

@author: devri.agustianto
"""

# -*- coding: utf-8 -*-
"""
Created on Tue Oct 22 17:05:12 2024

@author: devri.agustianto
Modified to use Fomel frequency analysis
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import MultiCursor
import segyio
import tkinter as tk
from tkinter import filedialog, simpledialog, ttk
from dlogst_spec_descriptor import dlogst_spec_descriptor
from fomel_freq import fomelfreq  # Changed to import fomelfreq

def load_segy_data():
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(title="Select SEG-Y file", filetypes=[("SEG-Y files", "*.sgy"), ("All files", "*.*")])
    if not file_path:
        print("No file selected. Exiting.")
        exit()
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segyio.tools.collect(segy.trace[:])
        dt = segy.bin[segyio.BinField.Interval] / 1_000_000
        num_samples = data.shape[1]
        time = np.arange(num_samples) * dt
    return data, time, dt

def get_plot_settings():
    root = tk.Tk()
    root.title("Set Plot Limits and Colormap")

    settings = {}
    entries = {}
    
    def on_ok():
        for key, (min_entry, max_entry) in entries.items():
            try:
                min_val = float(min_entry.get())
                max_val = float(max_entry.get())
                settings[key] = (min_val, max_val)
            except ValueError:
                print(f"Invalid input for {key}. Using default values.")
        settings['colormap'] = colormap_var.get()
        root.quit()
        root.destroy()

    fields = [
        ("Input Signal", (-1000, 1000)),
        ("Frequency", (0, 100)),
        ("Spectral Slope", (-5.0, 2.5)),
        ("Voice Slope", (-5, 5)),
        ("Magnitude * Voice Slope", (-2000, 2000)),
        ("Spectral Decrease", (0, 1)),
        ("Time (Y-axis)", (0, 5))
    ]

    for i, (name, default) in enumerate(fields):
        tk.Label(root, text=f"{name} Limits:").grid(row=i, column=0, sticky="e")
        min_entry = ttk.Entry(root)
        min_entry.insert(0, str(default[0]))
        min_entry.grid(row=i, column=1)
        tk.Label(root, text="to").grid(row=i, column=2)
        max_entry = ttk.Entry(root)
        max_entry.insert(0, str(default[1]))
        max_entry.grid(row=i, column=3)
        entries[name] = (min_entry, max_entry)

    tk.Label(root, text="Select Colormap:").grid(row=len(fields), column=0, sticky="e")
    colormap_options = ['viridis', 'plasma', 'inferno', 'magma', 'cividis', 'jet', 'seismic']
    colormap_var = tk.StringVar(root)
    colormap_var.set(colormap_options[0])
    colormap_menu = ttk.OptionMenu(root, colormap_var, *colormap_options)
    colormap_menu.grid(row=len(fields), column=1, columnspan=3, sticky="ew")

    ttk.Button(root, text="OK", command=on_ok).grid(row=len(fields)+1, column=1, columnspan=2)
    
    root.mainloop()
    return settings

# Load SEG-Y data
data, time, dt = load_segy_data()

trace_indices = {
    'W_BTP': 189,
    'B_1': 460,
    'TN_S253': 1432
}

shape, kmax, int_val = 0.35, 120, 35
fmax = data.shape[1] // 2

# Fomel analysis parameters
tsmo = 0.2  # smoothing window half-width
lambda_param = 0.1  # stabilization constant

# Get all plot settings at once
plot_settings = get_plot_settings()

# Create a list to store all figure and axes objects
all_figs = []
all_axs = []

for trace_name, trace_index in trace_indices.items():
    trace_data = data[trace_index, :]
    
    # Calculate Fomel frequencies
    freqloc, freqins = fomelfreq(trace_data, time, tsmo, lambda_param)
    
    MST, mag, fasa, voice, peak_freq, freq_dlogst, spec_centroid, spec_slope,\
            mag_voice_slope, voice_slope, spec_decrease, time_dlogst, freqst = \
            dlogst_spec_descriptor(trace_data, dt, fmax, shape, kmax, int_val)

    freq_limit_index = np.argmin(np.abs(freqst - plot_settings['Frequency'][1]))

    # Create figure with 10 subplots (added one for instantaneous frequency)
    fig, axs = plt.subplots(1, 10, figsize=(33, 10), sharey=True)
    all_figs.append(fig)
    all_axs.append(axs)
    fig.suptitle(f'Spectral Descriptors for {trace_name}', fontsize=16)

    # Plot input signal
    axs[0].plot(trace_data, time)
    axs[0].set_title('Input Signal')
    axs[0].set_ylabel('Time (s)')
    axs[0].set_xlabel('Amplitude')
    axs[0].set_xlim(plot_settings['Input Signal'])

    # Plot magnitude spectrogram
    im = axs[1].pcolormesh(freqst[:freq_limit_index], time, mag[:freq_limit_index, :].T, 
                           shading='auto', cmap=plot_settings['colormap'])
    axs[1].set_title('Magnitude Spectrogram')
    axs[1].set_xlabel('Frequency (Hz)')
    axs[1].set_xlim(plot_settings['Frequency'])
    plt.colorbar(im, ax=axs[1])

    # Plot magnitude*voice
    mag_voice = mag * voice
    im = axs[2].pcolormesh(freqst[:freq_limit_index], time, mag_voice[:freq_limit_index, :].T, 
                           shading='auto', cmap=plot_settings['colormap'])
    axs[2].set_title('Magnitude * Voice')
    axs[2].set_xlabel('Frequency (Hz)')
    axs[2].set_xlim(plot_settings['Frequency'])
    plt.colorbar(im, ax=axs[2])

    # Plot Fomel local frequency
    axs[3].plot(freqloc, time)
    axs[3].set_title('Fomel Local Frequency')
    axs[3].set_xlabel('Frequency (Hz)')
    axs[3].set_xlim(plot_settings['Frequency'])

    # Plot Fomel instantaneous frequency
    axs[4].plot(freqins, time)
    axs[4].set_title('Instantaneous Frequency')
    axs[4].set_xlabel('Frequency (Hz)')
    axs[4].set_xlim(plot_settings['Frequency'])

    # Plot spectral centroid
    axs[5].plot(spec_centroid, time)
    axs[5].set_title('Spectral Centroid')
    axs[5].set_xlabel('Frequency (Hz)')
    axs[5].set_xlim(plot_settings['Frequency'])

    # Plot spectral slope
    axs[6].plot(spec_slope, time)
    axs[6].set_title('Spectral Slope')
    axs[6].set_xlabel('Slope')
    axs[6].set_xlim(plot_settings['Spectral Slope'])

    # Plot voice slope
    axs[7].plot(voice_slope, time)
    axs[7].set_title('Voice Slope')
    axs[7].set_xlabel('Slope')
    axs[7].set_xlim(plot_settings['Voice Slope'])

    # Plot magnitude * voice slope
    axs[8].plot(mag_voice_slope, time)
    axs[8].set_title('Magnitude * Voice Slope')
    axs[8].set_xlabel('Slope')
    axs[8].set_xlim(plot_settings['Magnitude * Voice Slope'])

    # Plot spectral decrease
    axs[9].plot(spec_decrease, time)
    axs[9].set_title('Spectral Decrease')
    axs[9].set_xlabel('Decrease')
    axs[9].set_xlim(plot_settings['Spectral Decrease'])

    # Set y-axis limits and labels
    for ax in axs:
        ax.set_ylim(plot_settings['Time (Y-axis)'][::-1])  # Reverse for correct orientation
        ax.set_ylabel('Time (s)')

    plt.tight_layout()
    plt.subplots_adjust(top=0.9)

    print(f"Time range for {trace_name}: {time[0]:.2f}s to {time[-1]:.2f}s")

# Apply MultiCursor to all figures
all_axes = [ax for axs in all_axs for ax in axs]  # Flatten the list of axes
multi = MultiCursor(plt.gcf().canvas, all_axes, color='r', lw=1, horizOn=True, vertOn=True)

# Ensure the canvas is redrawn to avoid trailing lines
for fig in all_figs:
    fig.canvas.draw_idle()

plt.show()