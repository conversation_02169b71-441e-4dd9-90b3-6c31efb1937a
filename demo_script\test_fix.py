#!/usr/bin/env python3
"""
Simple test script to verify the divide by zero fix in superlet.py
"""

import numpy as np
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from superlet import computeWaveletSize, morlet

def test_zero_frequency():
    """Test the fix for zero frequency case"""
    print("Testing zero frequency case...")
    
    try:
        # Test computeWaveletSize with fc=0
        result = computeWaveletSize(fc=0, nc=3, fs=1000)
        print(f"computeWaveletSize(fc=0, nc=3, fs=1000) = {result}")
        
        # Test morlet function with fc=0
        wavelet = morlet(fc=0, nc=3, fs=1000)
        print(f"morlet(fc=0, nc=3, fs=1000) created wavelet of length {len(wavelet)}")
        
        # Test with very small frequency
        result_small = computeWaveletSize(fc=1e-8, nc=3, fs=1000)
        print(f"computeWaveletSize(fc=1e-8, nc=3, fs=1000) = {result_small}")
        
        print("SUCCESS: All tests passed! The divide by zero issue is fixed.")
        return True
        
    except Exception as e:
        print(f"FAILED: Test failed with error: {e}")
        return False

def test_normal_frequency():
    """Test that normal frequencies still work correctly"""
    print("\nTesting normal frequency cases...")
    
    try:
        # Test with normal frequencies
        for fc in [1, 10, 50, 100]:
            result = computeWaveletSize(fc=fc, nc=3, fs=1000)
            print(f"computeWaveletSize(fc={fc}, nc=3, fs=1000) = {result}")
            
        print("SUCCESS: Normal frequency tests passed!")
        return True
        
    except Exception as e:
        print(f"FAILED: Normal frequency test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing the divide by zero fix in superlet.py\n")
    
    success1 = test_zero_frequency()
    success2 = test_normal_frequency()
    
    if success1 and success2:
        print("\nAll tests passed! The fix is working correctly.")
    else:
        print("\nSome tests failed.")