# -*- coding: utf-8 -*-
"""
Created on Tue Oct 22 17:22:54 2024

@author: devri.agustianto
"""

import numpy as np
from scipy.signal import hilbert
from scipy.linalg import solve

def triang(N):
    """
    Create a triangular window of length N.
    
    Parameters:
    -----------
    N : int
        Length of the window (should be odd)
        
    Returns:
    --------
    window : ndarray
        Triangular window
    """
    if N % 2 == 0:
        raise ValueError("N should be odd")
    n = np.arange(N)
    half = (N-1)/2
    window = 1 - np.abs(n - half)/half
    return window

def convmtx(v, n):
    """
    Create a convolution matrix.
    
    Parameters:
    -----------
    v : array_like
        Vector to create convolution matrix from
    n : int
        Number of columns in the output matrix
        
    Returns:
    --------
    matrix : ndarray
        Convolution matrix
    """
    v_len = len(v)
    rows = n + v_len - 1
    matrix = np.zeros((rows, n))
    
    for i in range(n):
        matrix[i:i+v_len, i] = v
        
    return matrix

def fomelfreq(s, t, tsmo, lambda_param):
    """
    Calculate local frequency based on <PERSON><PERSON><PERSON>'s method.
    
    Method from: "Local seismic attributes", by S. Fomel, Geophysics, 2007
    
    Parameters:
    -----------
    s : array_like
        Input signal
    t : array_like
        Time coordinate for s
    tsmo : float
        Halfwidth of triangle smoother (seconds)
    lambda_param : float
        Stabilization constant
        
    Returns:
    --------
    freqloc : ndarray
        Fomel's local frequency
    freqins : ndarray
        Classic instantaneous frequency
    """
    # Ensure input is a column vector
    s = np.asarray(s).ravel()
    
    # Calculate time step
    dt = t[1] - t[0]
    
    # Differentiate signal by central finite difference
    sprime = np.zeros_like(s)
    sprime[1:-1] = (s[2:] - s[:-2]) / (4 * dt * np.pi)
    
    # Compute Hilbert transforms
    sh = hilbert(s).imag  # Hilbert transform of signal
    sprimeh2 = hilbert(sprime).imag
    
    sprimeh = np.zeros_like(s)
    sprimeh[1:-1] = (sh[2:] - sh[:-2]) / (4 * dt * np.pi)
    
    # Numerator of equation 2
    top = s * sprimeh - sprime * sh
    
    # Denominator of equation 3
    bot = s**2 + sh**2
    
    # Create diagonal matrix D
    D = np.diag(bot)
    
    # Make the triangle smoothing matrix
    nsmo = int(round(tsmo/dt))
    tri = triang(2*nsmo + 1)
    tri = tri / np.sum(tri)  # Normalize
    
    # Create convolution matrix
    S = convmtx(tri, len(s))
    
    # Remove extra rows
    S = S[nsmo:-nsmo]
    
    # Build denominator of equation 7
    lambda_eye = lambda_param**2 * np.eye(len(s))
    Bot = lambda_eye + S @ (D - lambda_eye)
    
    # Calculate both frequencies
    freqloc = solve(Bot, S @ top)
    freqins = top / bot
    
    return freqloc, freqins