import numpy as np
import pyfftw
from scipy.interpolate import interp1d

def calc_freq_loc(mag, freqst, p=2):
    """
    Calculate local dominant frequency from magnitude spectrogram.
    
    Parameters:
    -----------
    mag : ndarray
        Magnitude spectrogram (frequency x time)
    freqst : ndarray
        Frequency array
    p : float, optional
        Spectral exponent for calculating local frequency (default=2)
        
    Returns:
    --------
    freq_loc : ndarray
        Local frequency computed from spectrogram
    """
    # Calculate dominant frequency for each time slice
    fdom = np.zeros(mag.shape[1])
    for k in range(mag.shape[1]):
        spec = mag[:, k] ** p
        fdom[k] = np.sum(freqst * spec) / np.sum(spec)
    
    return fdom

def dlogst_spec_descriptor(data, dt, fmax=None, shape=None, kmax=None, int_val=None):
    """
    Compute the MST by a frequency-dependent Gaussian window using FFTW.
    PROPOSED STANDARD GAUSSIAN WINDOW S MODIFIFIER (DEVRI (2019))

    STD(F) = 1/L(F)
    WHERE L(F) => kmax/(1 + (kmax/int - 1)*exp(-shape*freq))

    Parameters:
    data (array): a time series of a trace
    dt (float): time sampling rate
    fmax (float, optional): maximum frequency
    shape (float, optional): shape constant
    kmax (float, optional): kmax constant
    int_val (float, optional): intercept constant

    Returns:
    MST (array): a complex matrix (row is frequency and column is time)
    mag (array): magnitude of MST
    fasa (array): phase of MST
    voice (array): voice component
    peak_freq (array): peak frequency at each time
    freq_loc (array): local dominant frequency (similar to Gabor)
    spec_centroid (array): spectral centroid
    spec_slope (array): spectral slope
    mag_voice_slope (array): magnitude * voice slope
    voice_slope (array): voice slope
    spec_decrease (array): spectral decrease
    time (array): time samples
    freqst (array): frequency samples
    """
    # Calculate Default Parameters
    if fmax is None:
        fmax = len(data) // 2
    if shape is None:
        shape = 0.05
    if kmax is None:
        kmax = 0.25 * (1 / (2 * dt))
    if int_val is None:
        int_val = 0.1 * kmax

    # Ensure data is a 1D array
    data = np.asarray(data).flatten()

    # Length and time
    LS = len(data)
    time = np.arange(LS) * dt
    fnyq = 1 / (2 * dt)  # Nyquist Freq
    df = (2 * fnyq) / LS  # Delta frequency
    fsamp = 1 / dt

    # Calculate the frequency vector
    fmin = 0
    maxf = LS // 2
    freqsamplingrate = 1
    spe_nelements = int(np.ceil((maxf - fmin + 1) / freqsamplingrate))
    frek = (fmin + np.arange(spe_nelements) * freqsamplingrate) / (dt * LS)

    # Frequency vector
    idf = np.argmin(np.abs(frek - fmax))
    freqst = frek[:idf + 1]

    # Calculation in Fourier Domain
    t = np.concatenate((np.arange(1, LS // 2 + 1), np.arange(-LS // 2 + 1, 1))) / LS
    
    # Use FFTW for forward FFT
    fft_object = pyfftw.builders.fft(data, LS)
    Fd = fft_object()
    CFd = np.concatenate((Fd, Fd))

    # Clip Frequency to Fmax
    fmax = min(fmax, LS)

    # Calculation of the S-Transform in Alpha Domain for arbitrary sampling rate
    N2 = len(freqst)
    MST = np.zeros((N2, LS), dtype=complex)
    freqn = freqst * (2 * np.pi / fsamp)  # Normalized Frequency From Hz to rad/sample

    # Standard Deviation Logistic (std = 1/gbeta)
    kdiv = (kmax / int_val) - 1
    logit = kmax / (1 + kdiv * np.exp(-shape * freqst))
    stdq = 1 / logit
    stdq2 = stdq ** 2

    # Create FFTW objects for forward and inverse FFT
    fft_object = pyfftw.builders.fft(np.zeros(LS))
    ifft_object = pyfftw.builders.ifft(np.zeros(LS, dtype=complex))

    for i in range(1, N2):
        freq = freqst[i]

        # Wavelet in Time
        wdb = np.exp(-(t ** 2) / (2 * stdq2[i])) * (1 / (np.sqrt(2 * np.pi) * stdq[i]))

        # Wavelet in Frequency
        GWB = fft_object(wdb / np.sum(wdb))

        # Modified Stockwell Transform
        MST[i, :] = ifft_object(CFd[i:i + LS] * GWB)

    # Zero Freq Row Correction
    st0 = np.mean(data) * np.ones(LS)
    MST[0, :] = st0

    mag = np.abs(MST)
    fasa = np.angle(MST)

    # Additional spectral descriptors
    time = np.arange(LS) * dt
    wtt = np.outer((2 * np.pi * freqst), time)

    # Voice component
    vc_lst = mag * np.exp(1j * fasa)
    vbal = vc_lst * np.exp(1j * wtt)
    voice = np.real(vbal)
    voice[0, :] = st0

    # Calculate local frequency (similar to Gabor method)
    freq_loc = calc_freq_loc(mag, freqst)

    # Magnitude * Voice
    mag_voice = mag * voice

    # Peak frequency
    peak_freq = freqst[np.argmax(mag, axis=0)]
    
    # Spectral centroid
    spec_centroid = np.sum(freqst[:, np.newaxis] * mag, axis=0) / np.sum(mag, axis=0)

    # Spectral slope
    def spectral_slope(spec, freq):
        mean_freq = np.mean(freq)
        mean_spec = np.mean(spec)
        num = np.sum((freq - mean_freq) * (spec - mean_spec))
        den = np.sum((freq - mean_freq)**2)
        return num / den

    spec_slope = np.apply_along_axis(spectral_slope, 0, mag, freqst)
    voice_slope = np.apply_along_axis(spectral_slope, 0, voice, freqst)
    mag_voice_slope = np.apply_along_axis(spectral_slope, 0, mag_voice, freqst)

    # Spectral decrease
    def spectral_decrease(spec):
        k = np.arange(len(spec))
        k[0] = 1  # Avoid division by zero
        kinv = 1 / k
        return np.sum(kinv[1:] * (spec[1:] - spec[0])) / np.sum(spec[1:])

    spec_decrease = np.apply_along_axis(spectral_decrease, 0, mag)
   
    return MST, mag, fasa, voice, peak_freq, freq_loc, spec_centroid, spec_slope,\
           mag_voice_slope, voice_slope, spec_decrease, time, freqst