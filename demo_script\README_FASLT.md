# FASLT Demo Script

This document describes the `demo_faslt.py` script, which demonstrates the Fractional Adaptive Superlet Transform (FASLT) implementation.

## Overview

The FASLT demo script (`demo_faslt.py`) provides a comprehensive demonstration of the Fractional Adaptive Superlet Transform, showcasing its key features and advantages over traditional integer-only approaches.

## Key Features Demonstrated

### 1. Fractional Superresolution Orders
- Unlike traditional approaches that use only integer orders, FASLT supports fractional orders (e.g., 1-15.7)
- Provides smooth interpolation between integer orders for enhanced flexibility
- Allows finer control of the time-frequency trade-off

### 2. Superresolution Modes
- **Additive Superresolution**: Each wavelet adds cycles (c1 + order)
- **Multiplicative Superresolution**: Each wavelet multiplies cycles (c1 * order)
- Direct comparison between both modes

### 3. Parameter Exploration
- Tests different base cycle values (c1 = 1, 3, 5, 10)
- Shows impact of parameter choices on time-frequency resolution
- Demonstrates optimal parameter selection strategies

### 4. Comprehensive Analysis
- Basic wavelet transform (CWT) when order=None
- Multiple fractional order ranges
- Fractional vs integer order comparison
- Performance analysis and timing

## Generated Outputs

The script generates several visualization files in the `figures/` directory:

1. **faslt_signal.png** - The synthetic test signal
2. **faslt_wavelet_basic.png** - Basic wavelet transform (CWT)
3. **faslt_fractional_orders.png** - Different fractional order ranges
4. **faslt_superresolution_comparison.png** - Additive vs multiplicative modes
5. **faslt_parameter_exploration.png** - Different base cycle values
6. **faslt_fractional_vs_integer.png** - Fractional vs integer order comparison

## Usage

```bash
cd demo_script
python demo_faslt.py
```

## Requirements

- Python 3.6+
- NumPy
- SciPy
- Matplotlib

## Test Signal

The demo uses a synthetic signal containing:
- Multiple frequency bursts (20, 40, 60 Hz)
- Frequency-shifted contamination
- Additive noise for realism
- Variable cycle lengths for different components

## FASLT Advantages

1. **Enhanced Flexibility**: Fractional orders provide more granular control
2. **Smooth Interpolation**: Continuous transition between integer orders
3. **Better Resolution**: Optimal time-frequency trade-offs
4. **Dual Modes**: Both additive and multiplicative superresolution

## Technical Details

- **Sampling Rate**: 1024 Hz
- **Frequency Range**: 10-80 Hz (141 points)
- **Signal Length**: ~2156 samples
- **Order Ranges**: Various fractional ranges (e.g., 1-5.5, 1-15.7, 1-30.3)

## Performance Notes

- Execution time scales with order range and signal length
- Higher fractional orders provide better frequency resolution but take longer
- Multiplicative mode may be slower than additive mode for high orders

## Comparison with Other Implementations

The script focuses specifically on FASLT capabilities. For comparisons with other superlet implementations (ASLT, NFASLT), see `demo_faslt_full.py` (though some modules may have compatibility issues).

## Troubleshooting

If you encounter import errors:
1. Ensure you're running from the `demo_script` directory
2. Check that `faslt.py` exists in the `../code/` directory
3. Verify all required packages are installed

## References

- Moca, V.V., Nagy-Dăbăcan, A., Bârzan, H., Mureșan, R.C. "Time-frequency super-resolution with superlets" Nature Communications 11, 344 (2020)
- Original MATLAB implementation by Harald Bârzan
