# Superlets package initialization
# This makes the root directory a Python package for easier imports

# Import main modules for convenience
try:
    from code.superletcx import superlets
    from reference.dlogst_spec_descriptor import dlogst_spec_descriptor
except ImportError:
    # If relative imports fail, modules can still be imported individually
    pass

__version__ = "1.0.0"
__author__ = "Superlets Team"
